import cors from 'cors';
import express from 'express';
import swagger<PERSON>SD<PERSON> from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

import { errorRequestHandler } from './middleware/generalErrorRequestHandler.js';
import { mongoErrorHandler } from './middleware/mongoErrorHandler.js';
import { RouteConfig, routerConfig } from './routes/config.js';

const app = express();
app.use(express.json());
app.use((cors as (options: cors.CorsOptions) => express.RequestHandler)({}));

//swagger implementation
const domainUrl = process.env.DOMAIN_URL || 'http://localhost:3000';
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'LuxLet Car Rental API',
      version: '1.0.0',
      description:
        'Comprehensive API documentation for LuxLet car rental platform. This API provides endpoints for managing cars, bookings, users, payments, and more.',
      contact: {
        name: 'LuxLet Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: domainUrl,
        description: 'Production server'
      },
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter JWT token for authentication'
        },
        clientAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter client JWT token for authentication'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'failed'
            },
            message: {
              type: 'string',
              example: 'Error message'
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'success'
            },
            message: {
              type: 'string',
              example: 'Operation completed successfully'
            },
            data: {
              type: 'object'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts', './src/types/*.ts']
};
const swaggerDocs = swaggerJSDoc(options);
//Documentation
app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

routerConfig.forEach(({ route, router }: RouteConfig) => {
  app.use(route, router);
});

app.use(mongoErrorHandler);

app.use(errorRequestHandler);
app.use((_req, res) => res.status(404).json());

export default app;
