import cors from 'cors';
import express from 'express';
import swagger<PERSON>SDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

import { errorRequestHandler } from './middleware/generalErrorRequestHandler.js';
import { mongoErrorHandler } from './middleware/mongoErrorHandler.js';
import { RouteConfig, routerConfig } from './routes/config.js';

const app = express();

// Request timeout middleware - prevent hanging requests
app.use((req, res, next) => {
  // Set timeout for file upload routes to 5 minutes, others to 30 seconds
  const timeout = req.path.includes('/images') ? 300000 : 30000; // 5 minutes for uploads, 30 seconds for others

  req.setTimeout(timeout, () => {
    // const error = new Error(`Request timeout after ${timeout / 1000} seconds`);
    res.status(408).json({
      status: 'failed',
      message: 'Request timeout. Please try again.',
      error: 'REQUEST_TIMEOUT'
    });
  });

  res.setTimeout(timeout, () => {
    if (!res.headersSent) {
      res.status(408).json({
        status: 'failed',
        message: 'Response timeout. Please try again.',
        error: 'RESPONSE_TIMEOUT'
      });
    }
  });

  next();
});

app.use(express.json({ limit: '10mb' })); // Increase JSON limit for large payloads
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Handle form data
app.use((cors as (options: cors.CorsOptions) => express.RequestHandler)({}));

//swagger implementation
const domainUrl = process.env.DOMAIN_URL || 'http://localhost:3000';
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'LuxLet Car Rental API',
      version: '1.0.0',
      description:
        'Comprehensive API documentation for LuxLet car rental platform. This API provides endpoints for managing cars, bookings, users, payments, and more.',
      contact: {
        name: 'LuxLet Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: domainUrl,
        description: 'Production server'
      },
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter JWT token for authentication'
        },
        clientAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter client JWT token for authentication'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'failed'
            },
            message: {
              type: 'string',
              example: 'Error message'
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'success'
            },
            message: {
              type: 'string',
              example: 'Operation completed successfully'
            },
            data: {
              type: 'object'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts', './src/types/*.ts']
};
const swaggerDocs = swaggerJSDoc(options);
//Documentation
app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Detailed health check for debugging
app.get('/health/detailed', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development',
    cloudinary: {
      configured: !!(process.env.CLOUDINARY_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET)
    },
    mongodb: {
      configured: !!process.env.MONGO_URL
    }
  });
});

routerConfig.forEach(({ route, router }: RouteConfig) => {
  app.use(route, router);
});

app.use(mongoErrorHandler);

app.use(errorRequestHandler);
app.use((_req, res) => res.status(404).json());

export default app;
