type EmailTemplate = {
  template: string;
  subject: string;
};

export const emailTemplates: Record<string, EmailTemplate> = {
  USER_CREATED: {
    template: 'user_welcome_email.hbs',
    subject: 'Welcome  Onboard'
  },
  PASSWORD_RESET: {
    template: 'password_reset_email.hbs',
    subject: 'Reset Your Password'
  },
  WELCOME_EMAIL: {
    template: 'user_welcome_email.hbs',
    subject: 'You are Welcome Onboard'
  },
  TRANSACTION_SUCCESS: {
    template: 'transaction_success_email.hbs',
    subject: 'Your Transaction Was Successful'
  },
  BOOKING_CANCELED: {
    template: 'booking_canceled_email.hbs',
    subject: 'Your Booking Has Been Canceled'
  },
  BOOKING_SELF_CANCELED: {
    template: 'booking_self_canceled_email.hbs',
    subject: 'Booking Cancellation Confirmation'
  },
  OTP_REQUEST: {
    template: 'otp_request.hbs',
    subject: 'Your LuxLet OTP Code'
  },
  BOOKING_DECLINED_NOTIFICATION: {
    template: 'booking_declined_notification.hbs',
    subject: 'Booking Declined by Fleet Manager'
  }
};
