import { emailTemplates } from '../consts.js';
import Logger from '../libs/logger.js';
import Client from '../models/ClientModel/ClientModel.js';
import { ClientDoc } from '../types/client.js';
import { OTPResponse, OTPSendType } from '../types/otp.js';
import { sendMail } from './mail_helper.js';
import { sendSMSWithBulkSMSNigeria, sendWhatsAppMessage, validatePhoneNumber } from './user_helper.js';

export const sendOTPToClient = async (client: ClientDoc, sendType: OTPSendType): Promise<OTPResponse> => {
  if (!client) {
    return { success: false, otp: null, message: 'Client is required' };
  }

  // Validate required fields based on send type
  if (
    (sendType === OTPSendType.Email && !client.email) ||
    ((sendType === OTPSendType.SMS || sendType === OTPSendType.WhatsApp) && !client.phoneNumber)
  ) {
    return {
      success: false,
      otp: null,
      message: `${sendType} requires ${sendType === OTPSendType.Email ? 'email' : 'phone number'}`
    };
  }

  try {
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const expiredAt = new Date(Date.now() + 5 * 60 * 1000).toISOString();

    // Initialize token object if it doesn't exist
    const updateData = {
      token: {
        otp,
        expiredAt
      }
    };

    const updatedClient = await Client.findOneAndUpdate(
      { _id: client._id },
      { $set: updateData },
      { new: true }
    ).lean();

    if (!updatedClient) {
      Logger.error('Failed to update client with OTP:', { clientId: client._id.toString() });
      return { success: false, otp: null, message: 'Failed to update client with OTP' };
    }

    let sent = false;

    switch (sendType) {
      case OTPSendType.Email: {
        const eventType = 'OTP_REQUEST';
        const template = emailTemplates[eventType];
        if (!template) {
          Logger.error('Email template not found for event type:', eventType);
          return {
            success: false,
            otp: null,
            message: 'Email template not found'
          };
        }
        try {
          const emailSent = await sendMail({ doc: updatedClient, ...template }, updatedClient);
          if (!emailSent) {
            Logger.error('Failed to send email to:', updatedClient.email);
          } else {
            sent = true;
          }
        } catch (error) {
          Logger.error('Error sending email:', error);
        }
        break;
      }

      case OTPSendType.SMS:
        try {
          const { formattedNumber, isValid } = validatePhoneNumber(client.phoneNumber);
          if (!isValid) {
            Logger.error('Invalid phone number format for client:', { phoneNumber: client.phoneNumber });
            return { success: false, otp: null, message: 'Invalid phone number format' };
          }

          const { success, provider } = await sendSMSWithBulkSMSNigeria(formattedNumber, otp);
          sent = success;
          if (!success) {
            Logger.error(`Failed to send SMS via ${provider}`);
            return {
              success: false,
              otp: null,
              message: `Failed to send SMS via ${provider}`
            };
          }
        } catch (error) {
          Logger.error('Error sending SMS:', error);
          return {
            success: false,
            otp: null,
            message: 'Error sending SMS'
          };
        }
        break;

      case OTPSendType.WhatsApp:
        try {
          sent = await sendWhatsAppMessage(client.phoneNumber, otp);
          if (!sent) {
            Logger.error('Failed to send WhatsApp message');
          }
        } catch (error) {
          Logger.error('Error sending WhatsApp message:', error);
        }
        break;

      default:
        Logger.error(`Unsupported send type: ${String(sendType)}`);
    }

    if (!sent) {
      // If sending failed, remove the OTP from the client
      await Client.findByIdAndUpdate(client._id, { $unset: { token: 1 } });
      return {
        success: false,
        otp: null,
        message: `Failed to send OTP via ${sendType}`
      };
    }

    Logger.info('OTP sent successfully to client:', { clientId: client._id.toString() });
    return {
      success: true,
      otp: process.env.NODE_ENV === 'development' ? otp : null,
      message: 'OTP sent successfully'
    };
  } catch (error) {
    Logger.error('Error in sendOTPToClient:', error);
    return {
      success: false,
      otp: null,
      message: error instanceof Error ? error.message : 'Error sending OTP'
    };
  }
};
