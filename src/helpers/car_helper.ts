import mongoose, { Schema } from 'mongoose';

import { ESCORT_PRICES, MIN_KM, VAT } from '../libs/consts.js';
import Logger from '../libs/logger.js';
import Booking from '../models/BookingModel/BookingModel.js';
import Rating from '../models/RatingModel/RatingModel.js';
import { BOOKING_STATUS, BookingDoc } from '../types/booking.js';
import { CarDoc, SCHEDULE } from '../types/car.js';
import { AddressDoc } from '../types/client.js';

type RatingSummaryResult = {
  _id: Schema.Types.ObjectId;
  avgRating: number;
};
export const calculateRatingSummary = async (carId: string): Promise<number> => {
  const result: RatingSummaryResult[] = await Rating.aggregate([
    { $match: { carId: new mongoose.Types.ObjectId(carId) } },
    { $group: { _id: '$carId', avgRating: { $avg: '$rating' } } }
  ]);
  const averageRating = result?.[0]?.avgRating ?? 5;
  return averageRating;
};

export const cancelOldBookings = async () => {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    await Booking.updateMany(
      {
        createdAt: { $lt: twentyFourHoursAgo },
        status: BOOKING_STATUS.IN_PROGRESS
      },
      {
        $set: { status: BOOKING_STATUS.CANCELED }
      }
    );
    Logger.info(`Canceled bookings that were older than 24 hours.`);
  } catch (error) {
    console.error('Error canceling old bookings:', error);
  }
};

export const calculateDistance = (coords1: AddressDoc, coords2: AddressDoc): number => {
  const toRadians = (degrees: number): number => (degrees * Math.PI) / 180;

  const R = 6371; // Radius of Earth in kilometers

  const lat1 = toRadians(coords1.latitude);
  const lon1 = toRadians(coords1.longitude);
  const lat2 = toRadians(coords2.latitude);
  const lon2 = toRadians(coords2.longitude);

  const dLat = lat2 - lat1;
  const dLon = lon2 - lon1;

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distanceKm = R * c; // Distance in kilometers
  return formatDistanceKm(distanceKm); // Round to 2 decimal places
};

// Helper function to format distance values consistently in kilometers with 2 decimal places
export const formatDistanceKm = (distanceKm: number): number => {
  return Math.round(distanceKm * 100) / 100;
};

export const isCarAvailableBySchedule = (carSchedule: SCHEDULE, timezone?: string): boolean => {
  // Get current server time in the specified timezone (default to Nigeria timezone)
  const targetTimezone = timezone || 'Africa/Lagos';

  try {
    // Get current time in the target timezone
    const now = new Date();
    const currentTimeInTimezone = new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    }).format(now);

    const [hours, minutes] = currentTimeInTimezone.split(':').map(Number);
    const currentTimeInMinutes = hours * 60 + minutes;

    // Define time ranges (in minutes from midnight)
    const nightStart = 15 * 60; // 3:00 PM (15:00)
    const nightEnd = 3 * 60; // 3:00 AM (03:00)

    // Determine if the current time is during night hours
    // Night is from 3 PM to 3 AM (next day)
    const isNightTime = currentTimeInMinutes >= nightStart || currentTimeInMinutes <= nightEnd;

    switch (carSchedule) {
      case SCHEDULE.DAY_NIGHT:
        return true; // Available 24/7
      case SCHEDULE.DAY_ONLY:
        return !isNightTime; // Available only during day (3 AM to 3 PM)
      case SCHEDULE.NIGHT_ONLY:
        return isNightTime; // Available only during night (3 PM to 3 AM)
      default:
        return true;
    }
  } catch (error) {
    // If timezone parsing fails, log error and return true (fail-safe)
    Logger.error(`Error parsing timezone ${targetTimezone}: ${error}`);
    return true;
  }
};

// Helper function to get current time in a specific timezone for debugging/logging
export const getCurrentTimeInTimezone = (timezone: string = 'Africa/Lagos'): string => {
  try {
    const now = new Date();
    return new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(now);
  } catch (error) {
    Logger.error(`Error getting time for timezone ${timezone}: ${error}`);
    return new Date().toISOString();
  }
};

export const calculateBookingCharges = (car: CarDoc, newBooking: BookingDoc) => {
  // Ensure minimum of 1 day for booking
  const numOfDays = Math.max(newBooking.numberOfDays || 1, 1);

  // Calculate escort charges only if escorts are requested
  const escortCount = newBooking.escortCount || 0;
  const escortDays = newBooking.escortDays || 0;

  let escortPayments = 0;
  if (escortCount > 0 && escortDays > 0) {
    const escortPrice =
      newBooking.pickupAddress.state.toLowerCase() === newBooking.destinationAddress.state.toLowerCase()
        ? ESCORT_PRICES.withinState
        : ESCORT_PRICES.outsideState;
    escortPayments = escortCount * escortPrice * escortDays;
  }

  const { destinationKilometer, pickupKilometer } = newBooking;

  const totalKilometer = destinationKilometer + pickupKilometer;

  // Calculate base price based on distance and additional stops
  const distancePrice =
    newBooking.hasAdditionalStop || totalKilometer > MIN_KM
      ? car.dailyPrice * numOfDays
      : car.dailyMinPrice * numOfDays;

  // Calculate VAT on the subtotal (distance price + escort payments)
  const subtotal = distancePrice + escortPayments;
  const vatCalculation = subtotal * VAT;

  // Calculate manager's share (80% of distance price, excluding VAT and escort fees)
  const managerTotal = distancePrice * 0.8;

  return {
    total: Number(Math.ceil(subtotal + vatCalculation)),
    managerTotal: Number(managerTotal.toFixed(2)),
    escortsTotal: Number(escortPayments.toFixed(2)),
    vat: Number(vatCalculation.toFixed(2)),
    subtotal: Number(subtotal.toFixed(2)),
    distancePrice: Number(distancePrice.toFixed(2))
  };
};
