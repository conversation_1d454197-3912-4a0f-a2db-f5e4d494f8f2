import fs from 'fs';
import handlebars from 'handlebars';
import nodemailer from 'nodemailer';
import path from 'path';
import { fileURLToPath } from 'url';

import Logger from '../libs/logger.js';
import User from '../models/UserModel/UserModel.js';
import { ClientDoc } from '../types/client.js';
import { USER_ROLE, UserDoc } from '../types/user.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the root directory of the project
const rootDir = path.resolve(__dirname, '../../');

const FROM_EMAIL = process.env.FROM_EMAIL;
const FROM_NAME = process.env.FROM_NAME;
const SMTP_HOST = process.env.SMTP_HOST ?? '';
const SMTP_PORT = parseInt(process.env.SMTP_PORT ?? '587', 10);
const SMTP_USER = process.env.SMTP_USER ?? '';
const SMTP_PASSWORD = process.env.SMTP_PASSWORD ?? '';

type ExtendedFields = {
  createdAt: Date | string;
};

type TriggerEmailParams<T extends ExtendedFields> = {
  doc: T;
  roles?: USER_ROLE[];
  template: string;
  subject: string;
};

const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: SMTP_PORT,
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASSWORD
  }
});

export const sendMail = async <T extends ExtendedFields>(data: TriggerEmailParams<T>, user: UserDoc | ClientDoc) => {
  try {
    // Validate email configuration
    if (!FROM_EMAIL || !FROM_NAME || !SMTP_HOST || !SMTP_PORT || !SMTP_USER || !SMTP_PASSWORD) {
      Logger.error(
        `Email configuration is incomplete. Please check environment variables. ${JSON.stringify(
          {
            FROM_EMAIL: !!FROM_EMAIL,
            FROM_NAME: !!FROM_NAME,
            SMTP_HOST: !!SMTP_HOST,
            SMTP_PORT: !!SMTP_PORT,
            SMTP_USER: !!SMTP_USER,
            SMTP_PASSWORD: !!SMTP_PASSWORD
          },
          null,
          2
        )}`
      );
      return false;
    }

    // Validate user email
    if (!user.email) {
      Logger.error('No email address provided');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)) {
      Logger.error(`Invalid email address: ${user.email}`);
      return false;
    }

    const emailData = {
      user,
      entity: data.doc,
      subject: data.subject,
      template: data.template,
      year: new Date().getFullYear(),
      formattedDate: new Date(data.doc.createdAt).toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
      })
    };

    // Try different paths for the template file
    const possiblePaths = [
      path.join(__dirname, `../email_templates/${emailData.template}`),
      path.join(rootDir, 'src/email_templates', emailData.template),
      path.join(rootDir, 'email_templates', emailData.template),
      path.join(rootDir, 'dist/email_templates', emailData.template),
      path.join(rootDir, 'dist/src/email_templates', emailData.template)
    ];

    let templatePath = null;
    for (const p of possiblePaths) {
      if (fs.existsSync(p)) {
        templatePath = p;
        Logger.info(`Found template at: ${p}`);
        break;
      }
    }

    if (!templatePath) {
      Logger.error(
        `Email template not found in any of the possible locations: ${JSON.stringify(
          {
            template: emailData.template,
            searchedPaths: possiblePaths,
            currentDir: __dirname,
            rootDir
          },
          null,
          2
        )}`
      );
      return false;
    }

    // Read and compile template
    const source = fs.readFileSync(templatePath, 'utf-8');
    const emailTemplate = handlebars.compile(source);

    // Prepare email message
    const message = {
      from: `"${FROM_NAME}" <${FROM_EMAIL}>`,
      to: user.email,
      subject: emailData.subject,
      html: emailTemplate(emailData)
    };

    Logger.info(
      `Attempting to send email with configuration: ${JSON.stringify(
        {
          from: message.from,
          to: message.to,
          subject: message.subject,
          smtpHost: SMTP_HOST,
          smtpPort: SMTP_PORT,
          smtpUser: SMTP_USER ? '***' : undefined
        },
        null,
        2
      )}`
    );

    // Send email
    const info = await transporter.sendMail(message);
    Logger.info(
      `Email sent successfully: ${JSON.stringify(
        {
          to: user.email,
          subject: emailData.subject,
          messageId: info.messageId
        },
        null,
        2
      )}`
    );
    return true;
  } catch (error) {
    Logger.error(
      `Failed to send email: ${JSON.stringify(
        {
          error:
            error instanceof Error
              ? {
                  message: error.message,
                  stack: error.stack,
                  name: error.name
                }
              : error,
          to: user.email,
          subject: data.subject,
          smtpConfig: {
            host: SMTP_HOST,
            port: SMTP_PORT,
            user: SMTP_USER ? '***' : undefined
          }
        },
        null,
        2
      )}`
    );
    return false;
  }
};

export const triggerEmail = async <T extends ExtendedFields>(document: TriggerEmailParams<T>) => {
  const users = await User.find({ role: { $in: document.roles } }).lean();
  for (const user of users) {
    await sendMail(document, user);
  }
  return true;
};
