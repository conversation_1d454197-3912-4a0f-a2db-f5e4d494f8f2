import { emailTemplates } from '../consts.js';
import Logger from '../libs/logger.js';
import Booking from '../models/BookingModel/BookingModel.js';
import Client from '../models/ClientModel/ClientModel.js';
import { BOOKING_STATUS, BookingDoc } from '../types/booking.js';
import { TransactionDoc } from '../types/transactions.js';
import { sendMail } from './mail_helper.js';

export const sendTransactionReceiptEmail = async (transaction: TransactionDoc, booking: BookingDoc) => {
  try {
    const eventType = 'TRANSACTION_SUCCESS';
    const result = emailTemplates[`${eventType}`];
    const user = await Client.findOne({ email: transaction.email }).lean();
    if (result) {
      const { template, subject } = result;
      const emailData = { doc: { ...transaction, ...user, ...booking }, template, subject };
      await sendMail(emailData, user);
    } else {
      Logger.error(`Email template not found for the given event type: ${eventType}`);
    }
  } catch (error) {
    Logger.error(`Error sending transaction receipt email: ${error}`);
  }

  return true;
};

export const sendCanceledBookingEmail = async (canceledBooking: BookingDoc[], isAutoCanceled = true) => {
  try {
    // Use different template based on cancellation type
    const eventType = isAutoCanceled ? 'BOOKING_CANCELED' : 'BOOKING_SELF_CANCELED';
    const result = emailTemplates[`${eventType}`];

    for (const booking of canceledBooking) {
      const user = await Client.findOne({ _id: booking.clientId }).lean();
      if (result && user) {
        const { template, subject } = result;

        // For auto-canceled bookings, we keep the array format
        if (isAutoCanceled) {
          const emailData = { doc: { 0: booking, ...user }, template, subject };
          await sendMail(emailData, user);
        } else {
          // For self-canceled bookings, we use direct properties
          const emailData = { doc: { ...booking, ...user }, template, subject };
          await sendMail(emailData, user);
        }
      } else {
        Logger.error(`Email template not found or user not found for booking: ${String(booking._id)}`);
      }
    }
  } catch (error) {
    Logger.error(`Error sending canceled booking email: ${error}`);
  }

  return true;
};

/**
 * Enhanced function to handle auto-cancellation of conflicting pending bookings
 * when a booking is successfully paid for.
 *
 * This function:
 * - Finds all pending bookings for the same car
 * - Identifies date conflicts with the paid booking
 * - Cancels conflicting bookings
 * - Sends cancellation emails to affected clients
 * - Provides detailed logging for audit purposes
 */
export const cancelConflictingBookings = async (paidBooking: BookingDoc) => {
  try {
    Logger.info(
      `Starting conflict resolution for paid booking: ${String(paidBooking._id)} on car: ${String(paidBooking.car)}`
    );

    // Find all pending bookings for the same car (excluding the paid booking)
    const pendingBookings = await Booking.find({
      car: paidBooking.car,
      _id: { $ne: paidBooking._id },
      status: BOOKING_STATUS.PENDING
    }).lean();

    if (pendingBookings.length === 0) {
      Logger.info(`No pending bookings found for car: ${String(paidBooking.car)}`);
      return { canceledCount: 0, conflictingBookings: [] };
    }

    Logger.info(`Found ${pendingBookings.length} pending bookings to check for conflicts`);

    // Parse dates for the paid booking
    const paidStartDate = new Date(paidBooking.startDate);
    const paidEndDate = new Date(paidBooking.endDate);

    // Filter bookings that have date conflicts with the paid booking
    const conflictingBookings = pendingBookings.filter(booking => {
      const bookingStartDate = new Date(booking.startDate);
      const bookingEndDate = new Date(booking.endDate);

      // Enhanced date conflict detection
      const hasOverlap =
        // Booking starts within paid period
        (bookingStartDate >= paidStartDate && bookingStartDate <= paidEndDate) ||
        // Booking ends within paid period
        (bookingEndDate >= paidStartDate && bookingEndDate <= paidEndDate) ||
        // Booking completely encompasses paid period
        (bookingStartDate <= paidStartDate && bookingEndDate >= paidEndDate);

      if (hasOverlap) {
        Logger.info(
          `Conflict detected - Booking ${String(booking._id)}: ` +
            `${bookingStartDate.toISOString()} to ${bookingEndDate.toISOString()} ` +
            `conflicts with paid booking: ${paidStartDate.toISOString()} to ${paidEndDate.toISOString()}`
        );
      }

      return hasOverlap;
    });

    if (conflictingBookings.length === 0) {
      Logger.info(`No conflicting bookings found for car: ${String(paidBooking.car)}`);
      return { canceledCount: 0, conflictingBookings: [] };
    }

    // Update all conflicting bookings to canceled status
    const bookingIds = conflictingBookings.map(booking => booking._id);

    const updateResult = await Booking.updateMany(
      { _id: { $in: bookingIds } },
      {
        $set: {
          status: BOOKING_STATUS.CANCELED,
          canceledAt: new Date(),
          cancelReason: 'Auto-canceled due to date conflict with paid booking'
        }
      }
    );

    Logger.info(`Successfully canceled ${updateResult.modifiedCount} conflicting bookings`);

    // Send cancellation emails to affected clients
    try {
      await sendCanceledBookingEmail(conflictingBookings, true);
      Logger.info(`Cancellation emails sent for ${conflictingBookings.length} bookings`);
    } catch (emailError) {
      Logger.error(`Error sending cancellation emails: ${emailError}`);
      // Don't throw here as the main operation (canceling bookings) was successful
    }

    Logger.info(
      `Conflict resolution completed - Auto-canceled ${conflictingBookings.length} conflicting bookings for car: ${String(paidBooking.car)}`
    );

    return {
      canceledCount: conflictingBookings.length,
      conflictingBookings: conflictingBookings.map(b => String(b._id))
    };
  } catch (error) {
    Logger.error(`Error in cancelConflictingBookings: ${error}`);
    throw error; // Re-throw to allow calling function to handle
  }
};
