import { Agenda } from 'agenda';
import { v2 as cloudinary } from 'cloudinary';
import { NextFunction, Request, Response } from 'express';
import fs from 'fs';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';

import Logger from '../libs/logger.js';
import CarCategory from '../models/CarCategoryModel/CarCategoryModel.js';
import CarImage from '../models/CarImageModel/CarImageModel.js';
import Car from '../models/CarModel/CarModel.js';
import { CarDoc } from './../types/car.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Enhanced Image Upload Configuration for Car Images
 *
 * File Size: 5MB - Optimal balance between quality and upload speed
 * Supported Formats: PNG, JPG, JPEG, WebP (high-quality formats only)
 *
 * Image Processing:
 * - Main image: 1200x800px (3:2 aspect ratio, standard for car listings)
 * - Medium: 800x600px (4:3 aspect ratio, for gallery views)
 * - Thumbnail: 400x300px (4:3 aspect ratio, for list views)
 *
 * Quality Settings:
 * - Auto quality optimization with 'good' baseline
 * - Progressive JPEG loading for better UX
 * - WebP format delivery for modern browsers
 * - Smart cropping with auto gravity
 */
const FILE_SIZE = 1048576 * 5; // 5MB - Optimal for high-quality images

type JobDoc = {
  fileName: string;
  filePath: string;
  originalname: string;
  data: CarDoc;
  model: string;
};

const storage = multer.diskStorage({
  destination: (_req, file, cb) => {
    cb(null, path.join(__dirname));
  },
  filename: (req, file, cb) => {
    const fileName = file.originalname.toLowerCase().split(' ').join('-');
    cb(null, `${Date.now()}-${fileName}`);
  }
});

export const upload = multer({
  storage: storage,
  limits: {
    fieldNameSize: 300,
    fileSize: FILE_SIZE
  },
  fileFilter: (req, file, cb) => {
    // Only allow high-quality image formats for car images
    const allowedImageTypes = [
      'image/png',
      'image/jpg',
      'image/jpeg',
      'image/webp' // Modern format for better compression
    ];

    // Check file type first
    if (!allowedImageTypes.includes(file.mimetype)) {
      return cb(new Error('Only high-quality image formats (PNG, JPG, JPEG, WebP) are allowed for car images!'));
    }

    // Check file size if content-length header is available
    const contentLength = req.headers['content-length'];
    if (contentLength) {
      const fileSize = parseInt(contentLength);
      if (fileSize > FILE_SIZE) {
        return cb(new Error('File size too large, should not be larger than 5MB'));
      }
    }

    // File passes all checks
    cb(null, true);
  }
});

// Multer error handling middleware
export const handleMulterError = (err: unknown, req: Request, res: Response, next: NextFunction): void => {
  if (err instanceof multer.MulterError) {
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        res.status(400).json({
          status: 'failed',
          message: 'File size too large. Maximum allowed size is 5MB.',
          error: 'FILE_SIZE_LIMIT_EXCEEDED'
        });
        return;
      case 'LIMIT_FILE_COUNT':
        res.status(400).json({
          status: 'failed',
          message: 'Too many files uploaded. Please upload fewer files.',
          error: 'FILE_COUNT_LIMIT_EXCEEDED'
        });
        return;
      case 'LIMIT_UNEXPECTED_FILE':
        res.status(400).json({
          status: 'failed',
          message: 'Unexpected file field. Please check your form data.',
          error: 'UNEXPECTED_FILE_FIELD'
        });
        return;
      default:
        res.status(400).json({
          status: 'failed',
          message: 'File upload error occurred.',
          error: err.code || 'UPLOAD_ERROR'
        });
        return;
    }
  } else if (
    err &&
    typeof err === 'object' &&
    'message' in err &&
    typeof (err as { message: unknown }).message === 'string'
  ) {
    const message = (err as { message: string }).message;
    if (message.includes('image formats')) {
      res.status(400).json({
        status: 'failed',
        message: 'Invalid file format. Please upload high-quality images (PNG, JPG, JPEG, WebP) only.',
        error: 'INVALID_FILE_FORMAT'
      });
      return;
    } else if (message.includes('File size')) {
      res.status(400).json({
        status: 'failed',
        message: 'File size too large. Maximum allowed size is 5MB.',
        error: 'FILE_SIZE_LIMIT_EXCEEDED'
      });
      return;
    } else {
      res.status(400).json({
        status: 'failed',
        message: message,
        error: 'FILE_UPLOAD_ERROR'
      });
      return;
    }
  }

  next(err);
};

// Validate Cloudinary configuration on startup
const validateCloudinaryConfig = () => {
  const requiredEnvVars = ['CLOUDINARY_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required Cloudinary environment variables: ${missingVars.join(', ')}`);
  }
};

// Validate configuration on module load
validateCloudinaryConfig();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Enhanced file cleanup function
const cleanupFile = (filePath: string, context: string) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      Logger.info(`Successfully cleaned up file: ${filePath} (${context})`);
    }
  } catch (cleanupError) {
    Logger.error(`Failed to cleanup file: ${filePath} (${context})`, cleanupError);
  }
};

export const manageFileUpload = async (filePath: string, fileName: string, originalname: string, data: any) => {
  let agenda: Agenda | null = null;

  try {
    // Validate required environment variables
    const mongoConnectionString = process.env.MONGO_URL;
    if (!mongoConnectionString) {
      throw new Error('MONGO_URL is not defined');
    }

    // Validate file exists before processing
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // Get file stats for logging
    const fileStats = fs.statSync(filePath);
    Logger.info(`Processing file upload: ${originalname} (${fileStats.size} bytes)`);

    agenda = new Agenda({
      db: { address: mongoConnectionString, collection: 'jobCollection' },
      processEvery: '10 seconds', // Process jobs every 10 seconds
      maxConcurrency: 5, // Limit concurrent jobs to prevent memory issues
      defaultConcurrency: 2 // Default concurrency for job types
    });

    agenda.define('Upload Images', async (job: any) => {
      const { filePath, fileName, originalname, data } = job.attrs.data as JobDoc;

      try {
        // Double-check file exists before upload
        if (!fs.existsSync(filePath)) {
          throw new Error(`File not found during upload: ${filePath}`);
        }

        Logger.info(`Starting Cloudinary upload for: ${originalname}`);
        await uploadToCloudinary({ filePath, fileName, originalname, data });
        Logger.info(`Successfully uploaded image: ${originalname} for car: ${data._id.toString()}`);

        // Clean up file after successful upload
        cleanupFile(filePath, 'successful upload');
      } catch (error) {
        Logger.error(`Failed to upload image: ${originalname} for car: ${data._id.toString()}`, error);

        // Clean up file after failed upload
        cleanupFile(filePath, 'failed upload');

        // Re-throw to mark job as failed
        throw error;
      }
    });

    // Set up error handling for agenda
    agenda.on('error', error => {
      Logger.error('Agenda error:', error);
    });

    agenda.on('fail', (error, job) => {
      Logger.error(`Job failed: ${job.attrs.name}`, error);
    });

    await agenda.start();

    // Schedule the job - simple one-time execution
    const job = await agenda.schedule('in 2 seconds', 'Upload Images', {
      filePath,
      fileName,
      data,
      originalname,
      model: 'cars'
    });

    Logger.info(`Image upload job scheduled for: ${originalname} (Job ID: ${job.attrs._id.toString()})`);
  } catch (error) {
    Logger.error('Error scheduling image upload job:', error);

    // Clean up the file if job scheduling fails
    cleanupFile(filePath, 'job scheduling failure');

    // Clean up agenda connection
    if (agenda) {
      try {
        await agenda.stop();
      } catch (stopError) {
        Logger.error('Error stopping agenda:', stopError);
      }
    }

    throw error; // Re-throw to let controller handle the error
  }
};

const uploadToCloudinary = async (job: any) => {
  const { fileName, filePath, originalname, data } = job as JobDoc;

  // Validate input data
  if (!filePath || !fileName || !data || !data._id) {
    throw new Error('Invalid job data: missing required fields');
  }

  // Check if file exists before processing
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }

  try {
    Logger.info(`Starting Cloudinary upload for: ${originalname}`);

    // Set upload timeout to prevent hanging requests
    const uploadTimeout = 120000; // 2 minutes timeout

    // Create a promise that rejects after timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Cloudinary upload timeout after ${uploadTimeout / 1000} seconds for file: ${originalname}`));
      }, uploadTimeout);
    });

    // Enhanced Cloudinary upload configuration for high-quality car images
    const uploadPromise = cloudinary.uploader.upload(filePath, {
      public_id: fileName,
      resource_type: 'image',
      timeout: uploadTimeout, // Set Cloudinary's internal timeout
      // Image optimization settings
      quality: 'auto:good', // Automatic quality optimization with good quality baseline
      format: 'auto', // Automatic format selection (WebP for modern browsers, fallback to original)
      // Transformation for consistent dimensions and quality
      transformation: [
        {
          // Resize to standard car image dimensions while maintaining aspect ratio
          width: 1200,
          height: 800,
          crop: 'fill', // Fill the dimensions, cropping if necessary
          gravity: 'auto', // Smart cropping focusing on the most important part
          quality: 'auto:good',
          fetch_format: 'auto' // Deliver in the best format for the user's browser
        },
        {
          // Additional optimization
          flags: 'progressive', // Progressive JPEG loading
          dpr: 'auto' // Automatic DPR (Device Pixel Ratio) adjustment
        }
      ],
      // Generate multiple sizes for responsive images
      eager: [
        {
          width: 400,
          height: 300,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          format: 'auto'
        },
        {
          width: 800,
          height: 600,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          format: 'auto'
        }
      ],
      // Folder organization
      folder: 'luxlet/car-images',
      // Additional metadata
      context: {
        car_id: data._id.toString(),
        company_id: data.company.toString(),
        upload_type: 'car_image'
      }
    });

    // Race between upload and timeout
    const result = (await Promise.race([uploadPromise, timeoutPromise])) as any;

    Logger.info(`Cloudinary upload successful for: ${originalname}`);

    const { asset_id, public_id, signature, format, url, secure_url } = result;

    // Generate responsive image URLs using Cloudinary's transformation API
    const baseUrl = secure_url.split('/upload/')[0] + '/upload/';
    const imagePath = secure_url.split('/upload/')[1];

    const responsiveUrls = {
      thumbnail: `${baseUrl}w_400,h_300,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`,
      medium: `${baseUrl}w_800,h_600,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`,
      large: `${baseUrl}w_1200,h_800,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`
    };

    // Save image record to database
    const carImage = new CarImage({
      asset_id,
      public_id,
      signature,
      format,
      url,
      secure_url,
      responsive_urls: responsiveUrls,
      model: 'cars',
      action: 'car-image-uploads',
      carId: data._id,
      companyId: data.company,
      isMain: originalname == data.mainImageId ? true : undefined,
      createdBy: data.createdBy
    });

    await carImage.save();
    Logger.info(`Car image record saved to database: ${carImage._id.toString()}`);

    // Update car with new image reference
    await Car.findOneAndUpdate({ _id: data._id }, { $push: { carImages: carImage._id }, createdBy: data.createdBy });
    Logger.info(`Car updated with new image reference: ${data._id.toString()}`);

    // Clean up local file
    fs.unlinkSync(filePath);
    Logger.info(`Local file cleaned up: ${filePath}`);
  } catch (error) {
    Logger.error(`Error processing image upload job for ${originalname}:`, error);

    // Clean up local file on error
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        Logger.info(`Local file cleaned up after error: ${filePath}`);
      }
    } catch (cleanupError) {
      Logger.error('Failed to cleanup file after upload error:', cleanupError);
    }

    throw error; // Re-throw to mark job as failed
  }
};

// Category image upload function
export const manageCategoryFileUpload = async (
  filePath: string,
  fileName: string,
  originalname: string,
  categoryId: string
) => {
  try {
    const mongoConnectionString = process.env.MONGO_URL;
    if (!mongoConnectionString) {
      throw new Error('MONGO_URL is not defined');
    }

    const agenda = new Agenda({
      db: { address: mongoConnectionString, collection: 'jobCollection' }
    });

    agenda.define('Upload Category Images', async job => {
      const { filePath, fileName, originalname, categoryId } = job.attrs.data as {
        filePath: string;
        fileName: string;
        originalname: string;
        categoryId: string;
      };
      await uploadCategoryToCloudinary({ filePath, fileName, originalname, categoryId });
    });

    await agenda.start();

    await agenda.schedule('in 2 seconds', 'Upload Category Images', {
      filePath,
      fileName,
      originalname,
      categoryId
    });

    Logger.info('Category image upload job scheduled');
  } catch (error) {
    console.error('Error scheduling category image upload:', error);
  }
};

const uploadCategoryToCloudinary = async (job: {
  filePath: string;
  fileName: string;
  originalname: string;
  categoryId: string;
}) => {
  const { fileName, filePath, categoryId } = job;
  try {
    // Upload category image to Cloudinary with multiple sizes
    const result = await cloudinary.uploader.upload(filePath, {
      public_id: fileName,
      resource_type: 'image',
      quality: 'auto:good',
      format: 'auto',
      transformation: [
        {
          width: 1024,
          height: 1024,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          fetch_format: 'auto'
        }
      ],
      eager: [
        {
          width: 256,
          height: 256,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          format: 'auto'
        },
        {
          width: 512,
          height: 512,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          format: 'auto'
        }
      ],
      folder: 'luxlet/category-images',
      context: {
        category_id: categoryId,
        upload_type: 'category_image'
      }
    });

    const { secure_url } = result;
    const baseUrl = secure_url.split('/upload/')[0] + '/upload/';
    const imagePath = secure_url.split('/upload/')[1];

    const thumbnailUrl = `${baseUrl}w_256,h_256,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`;
    const mediumUrl = `${baseUrl}w_512,h_512,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`;
    const bigUrl = `${baseUrl}w_1024,h_1024,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`;

    // Update category with image URLs
    await CarCategory.findOneAndUpdate(
      { _id: categoryId },
      {
        thumbnailUrl,
        mediumUrl,
        bigUrl
      }
    );

    fs.unlinkSync(filePath);
    Logger.info(`Category image uploaded successfully for category ${categoryId}`);
  } catch (error) {
    console.error('Error uploading category image:', error);
  }
};
