import firebase from '../helpers/firebase.js';
import Notification from '../models/NotificationModel/NotificationModel.js';

export const sendNotification = async (
  fcmToken: string,
  title: string,
  body: string,
  action: string,
  userId: string,
  clientId: string
) => {
  const message = {
    token: fcmToken,
    notification: {
      title,
      body
    },
    data: {
      action,
      title,
      body,
      userId: userId ? userId?.toString() : '',
      clientId: clientId ? clientId?.toString() : ''
    }
  };

  try {
    await firebase.messaging().send(message);
  } catch (error: unknown) {
    console.error('Error:', error);
  }

  const notification = new Notification({
    userId,
    clientId,
    action,
    body,
    title,
    fcmToken,
    isRead: false,
    isSent: true
  });
  await notification.save();
};
