/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import firebase from 'firebase-admin';
import { readFileSync } from 'fs';
import path from 'path';

import Logger from '../libs/logger.js';

const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH;

let firebaseApp: any;

if (!serviceAccountPath) {
  Logger.warn('SERVICE_ACCOUNT_KEY_PATH is not set. Firebase will not be initialized.');
  // Create a mock firebase object for testing
  firebaseApp = {
    messaging: () => ({
      send: () => Logger.info('Mock Firebase message sent')
    })
  };
} else {
  try {
    const serviceAccount = JSON.parse(readFileSync(path.resolve(serviceAccountPath), 'utf-8'));

    firebase.initializeApp({
      credential: firebase.credential.cert(serviceAccount)
    });
    firebaseApp = firebase;
  } catch (error) {
    Logger.error(`Failed to initialize Firebase: ${JSON.stringify(error, null, 2)} `);
    // Create a mock firebase object for testing
    firebaseApp = {
      messaging: () => ({
        send: () => Logger.info('Mock Firebase message sent')
      })
    };
  }
}

export default firebaseApp;
