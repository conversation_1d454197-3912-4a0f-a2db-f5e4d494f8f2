import crypto from 'crypto';

import { AddressDoc } from '../types/user.js';

export const formatCsvCell = (str: string) =>
  str
    // In case there is a quotation mark in the string already, it needs to be escaped too.
    // @see https://stackoverflow.com/a/44120055
    .replace(/"/g, '""')
    // Replace all white-space characters (including new line breaks) with a single space.
    // @see https://stackoverflow.com/a/45029224
    .replace(/\s+/g, ' ')
    .trim();

export const stringToBoolean = (str: string) => {
  return str === 'true';
};

export const generateRandomNumber = (len: number) => {
  const buffer = crypto.randomBytes(6);
  let randomNumberString = parseInt(buffer.toString('hex'), 16).toString();
  if (randomNumberString.length > len) {
    randomNumberString = randomNumberString.slice(0, 11);
  } else {
    while (randomNumberString.length < len) {
      randomNumberString += Math.floor(Math.random() * 10).toString();
    }
  }

  return randomNumberString;
};

export const addPointsToAddress = (address: AddressDoc) => {
  const addLocation = {
    type: 'Point',
    coordinates: [address.longitude, address.latitude]
  };
  address.location = addLocation;
  return address;
};

export const calculateDaysBetween = (date1: string, date2: string): number => {
  const parsedDate1 = new Date(date1);
  const parsedDate2 = new Date(date2);
  const timeDifference = parsedDate2.getTime() - parsedDate1.getTime();
  const differenceInDays = timeDifference / (1000 * 3600 * 24);
  return differenceInDays >= 1 ? Math.floor(differenceInDays) : 0;
};

export const constructDatesBetween = (startDate: string, endDate: string): Date[] => {
  const dates: Date[] = [];

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    throw new Error('Invalid date format. Ensure dates are in ISO format (YYYY-MM-DDTHH:MM:SS.sssZ)');
  }

  const current = new Date(start);

  while (current <= end) {
    dates.push(new Date(current));
    current.setDate(current.getDate() + 1);
  }

  return dates;
};

export const isValidDateString = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

export const isTodayGreaterThan = (date: string | Date): boolean => {
  const passedDate = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(passedDate.getTime())) {
    throw new Error('Invalid date format');
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today > passedDate;
};
