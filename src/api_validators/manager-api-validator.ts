import Joi from '@hapi/joi';

export const createManagerApiValidator = Joi.object({
  company_name: Joi.string().required(),
  support_phone: Joi.string().required(),
  fcmToken: Joi.string().required(),
  deviceId: Joi.string(),
  password: Joi.string().required(),
  currency: Joi.string().required(),
  support_email: Joi.string()
    .email({ tlds: { allow: false } })
    .required(),
  address: Joi.object({
    fullAddress: Joi.string().required(),
    longitude: Joi.number().required(),
    latitude: Joi.number().required(),
    state: Joi.string().required()
  }).required()
});
