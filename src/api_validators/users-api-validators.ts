import Joi from '@hapi/joi';

import { USER_ROLE } from '../types/user.js';

const addressSchema = Joi.object({
  fullAddress: Joi.string().required(),
  longitude: Joi.number().required(),
  latitude: Joi.number().required(),
  state: Joi.string().required(),
  postal: Joi.string(),
  city: Joi.string().required(),
  lga: Joi.string(),
  countryCode: Joi.string().required(),
  country: Joi.string().required(),
  neighborhood: Joi.string(),
  area: Joi.string()
});

export const createClientApiValidator = Joi.object({
  role: Joi.string().valid(...Object.values(USER_ROLE)),
  email: Joi.string().email({ tlds: { allow: false } }),
  phoneNumber: Joi.string().required(),
  deviceId: Joi.string().required(),
  fcmToken: Joi.string().required()
});

export const createFleetUserApiValidator = Joi.object({
  role: Joi.string().valid(USER_ROLE.MANAGER).required(),
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required(),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required'
  }),
  phoneNumber: Joi.string().required(),
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  middleName: Joi.string(),
  fcmToken: Joi.string().required(),
  deviceId: Joi.string().required(),
  address: addressSchema.required()
});

export const userLoginApiValidator = Joi.object({
  email: Joi.string().email({ tlds: { allow: false } }),
  phoneNumber: Joi.string(),
  password: Joi.string().required(),
  deviceId: Joi.string(),
  fcmToken: Joi.string()
});

export const updateUserApiValidator = Joi.object({
  phoneNumber: Joi.string(),
  email: Joi.string(),
  firstName: Joi.string(),
  lastName: Joi.string(),
  fcmToken: Joi.string(),
  deviceId: Joi.string(),
  middleName: Joi.string(),
  address: addressSchema,
  role: Joi.string().valid(...Object.values(USER_ROLE))
});

export const updateRoleApiValidator = Joi.object({
  role: Joi.string().valid(...Object.values(USER_ROLE))
});

export const changePasswordApiValidator = Joi.object({
  oldPassword: Joi.string().required(),
  newPassword: Joi.string().required(),
  email: Joi.string().required()
});

export const OtpApiValidator = Joi.object({
  email: Joi.string().email({ tlds: { allow: false } }),
  phoneNumber: Joi.string()
});

export const resetPasswordTokenApiValidator = Joi.object({
  phoneNumber: Joi.string(),
  email: Joi.string(),
  otp: Joi.string().required()
}).xor('phoneNumber', 'email');

export const passwordResetApiValidator = Joi.object({
  newPassword: Joi.string().required()
});

export const updatePasswordApiValidator = Joi.object({
  newPassword: Joi.string().required(),
  oldPassword: Joi.string().required()
});

export const resetDeviceIdApiValidator = Joi.object({
  phoneNumber: Joi.string().required(),
  otp: Joi.string().required()
});
