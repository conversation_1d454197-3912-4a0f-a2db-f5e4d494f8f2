import Joi from '@hapi/joi';

import { BOOKING_STATUS } from '../types/booking.js';

const apiAddressSchema = Joi.object({
  fullAddress: Joi.string().required(),
  longitude: Joi.number().required(),
  latitude: Joi.number().required(),
  state: Joi.string().required(),
  city: Joi.string().required(),
  countryCode: Joi.string().required(),
  country: Joi.string().required(),
  lga: Joi.string(),
  postal: Joi.string(),
  area: Joi.string(),
  neighborhood: Joi.string()
}).required();

export const createBookingApiValidator = Joi.object({
  carId: Joi.string().required(),
  startDate: Joi.string().required(),
  startTime: Joi.string().required(),
  endDate: Joi.string().required(),
  status: Joi.string().valid(...Object.values(BOOKING_STATUS)),
  escortDays: Joi.number(),
  pickupAddress: apiAddressSchema,
  destinationAddress: apiAddressSchema,
  escortCount: Joi.number().required(),
  hasAdditionalStop: Joi.boolean()
});
