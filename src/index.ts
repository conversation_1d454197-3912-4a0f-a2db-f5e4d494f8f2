import mongoose from 'mongoose';
import cron from 'node-cron';
import process from 'process';

import app from './app.js';
import { cancelOldBookings } from './helpers/car_helper.js';
import Logger from './libs/logger.js';
import { seedDBdata } from './models/db_seed.js';

const MONGO_URL = process.env.MONGO_URL;
const NODE_ENV = process.env.NODE_ENV || 'development';
const PORT = process.env.PORT || 8000;

if (!MONGO_URL) {
  Logger.error('MONGO_URL environment variable is not set');
  process.exit(1);
}

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGO_URL);
    Logger.info(`Connected to MongoDB at ${NODE_ENV} environment`);

    await seedDBdata();
    Logger.info('Database seeded successfully');
  } catch (error) {
    Logger.error('Error connecting to the database', error);
    process.exit(1);
  }
}

// Setup cron job with error handling
cron.schedule('0 0 * * *', async () => {
  try {
    Logger.info('Running job to cancel bookings older than 24 hours...');
    await cancelOldBookings();
    Logger.info('Successfully completed booking cancellation job');
  } catch (error) {
    Logger.error('Error in booking cancellation job', error);
  }
});

// Graceful shutdown
const signals = ['SIGTERM', 'SIGINT'];
signals.forEach(signal => {
  process.on(signal, async () => {
    Logger.info(`Received ${signal}. Starting graceful shutdown...`);
    try {
      await mongoose.connection.close();
      Logger.info('MongoDB connection closed');
      process.exit(0);
    } catch (error) {
      Logger.error('Error during shutdown', error);
      process.exit(1);
    }
  });
});

// Start the application
async function startServer() {
  await connectToDatabase();

  const server = app.listen(PORT, () => {
    Logger.info(`🌏 Server running on PORT: ${PORT}`);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', error => {
    Logger.error('Unhandled Promise Rejection', error);
    server.close(() => process.exit(1));
  });
}

startServer().catch(error => {
  Logger.error('Failed to start server', error);
  process.exit(1);
});
