import { Request, Response } from 'express';
import { Document } from 'mongoose';

import {
  createClientApiValidator,
  OtpApiValidator,
  resetDeviceIdApiValidator,
  updateUserApiValidator
} from '../api_validators/users-api-validators.js';
import { sendOTPToClient } from '../helpers/client_helper.js';
import { advancedResults } from '../helpers/query.js';
import { hasExpired, sanitizeClientUser, validatePhoneNumber } from '../helpers/user_helper.js';
import Client, { ClientDocumentResult } from '../models/ClientModel/ClientModel.js';
import { AUTH_CODES, ClientDoc, RegisterClientRequestBody } from '../types/client.js';
import { OTPResponse, OTPSendType } from '../types/otp.js';
import { RegisterUserRequestBody } from '../types/user.js';
import { asyncHandler } from './../helpers/asyncHandler.js';

export const clientAuthentication = asyncHandler(async (req: Request, res: Response) => {
  const { phoneNumber, deviceId, fcmToken } = req.body as RegisterClientRequestBody;
  const { error } = createClientApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
  if (!isValid) {
    return res.status(422).json({ error: `invalid phone number ${phoneNumber} provided` });
  }

  const clientUser = await Client.findOne({
    $or: [{ phoneNumber: phoneNumber }, { phoneNumber: formattedNumber }],
    isActive: true
  });

  if (clientUser) {
    if (clientUser.deviceId === deviceId) {
      const authToken = clientUser.getSignedJwtToken();
      const client = await Client.findOne<ClientDocumentResult>({ _id: clientUser._id }).lean();
      const sanitizedClient = sanitizeClientUser(client);
      await Client.findByIdAndUpdate({ _id: client._id }, { fcmToken, deviceId });
      return res.status(200).json({
        status: 'success',
        code: 'AUTH_300',
        message: AUTH_CODES.AUTH_300,
        data: { ...sanitizedClient, fcmToken, authToken }
      });
    }
    const otpResponse: OTPResponse = await sendOTPToClient(clientUser, OTPSendType.Email);
    return res.status(200).json({
      status: 'failed',
      code: 'AUTH_400',
      message: AUTH_CODES.AUTH_400,
      otp: process.env.NODE_ENV === 'development' ? otpResponse.otp : null
    });
  }

  const newClientUser = new Client({ phoneNumber: formattedNumber, deviceId, fcmToken });
  await newClientUser.save();
  const authToken = newClientUser.getSignedJwtToken();

  const client = await Client.findOne<ClientDocumentResult>({ _id: newClientUser._id }).lean();
  const returnedClient = sanitizeClientUser(client);

  return res.status(200).json({
    status: 'success',
    code: 'AUTH_200',
    message: AUTH_CODES.AUTH_200,
    data: { ...returnedClient, authToken }
  });
});

export const getClient = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const client = await Client.findOne<ClientDocumentResult>({ _id: id });
  if (!client) {
    return res.status(404).json({
      status: 'failed',
      message: `Client not found with id ${id}`
    });
  }
  const returnedUser = sanitizeClientUser(client._doc);
  return res.status(200).json({
    status: 'success',
    data: returnedUser
  });
});

export const updateClient = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterUserRequestBody;
  const client = req.client;
  const { lastName, firstName, email, fcmToken } = body;
  const { error } = updateUserApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  //const updateAddress = addPointsToAddress(address);
  const updatedClient = await Client.findOneAndUpdate<ClientDocumentResult>(
    { _id: client.id },
    { lastName, firstName, email, fcmToken },
    { new: true }
  );

  const returnedClient = sanitizeClientUser(updatedClient._doc);
  return res.status(200).json({
    status: 'success',
    message: 'Client Updated Successfully.',
    data: returnedClient
  });
});

export const deleteClient = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const user = await Client.findOneActive({ _id: id });
  if (!user) {
    return res.status(404).json({ error: `client not found with the id ${id} provided` });
  }

  const inactiveClient = await Client.findOneAndUpdate<ClientDocumentResult>(
    { _id: id },
    { is_active: false },
    { new: true }
  );

  const returnedUser = sanitizeClientUser(inactiveClient._doc);
  return res.status(200).json({
    status: 'success',
    message: 'Client deactivated Successfully.',
    data: returnedUser
  });
});

export const sendClientOTP = asyncHandler(async (req: Request, res: Response) => {
  const { phoneNumber } = req.body as { phoneNumber: string };

  const { error } = OtpApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
  if (!isValid) {
    return res.status(422).json({ error: `Invalid phone number ${phoneNumber} provided` });
  }

  const client = await Client.findOne({
    $or: [{ phoneNumber: phoneNumber }, { phoneNumber: formattedNumber }],
    isActive: true
  });
  if (!client) {
    return res.status(404).json({
      status: 'failed',
      message: 'No client found with the provided phone number'
    });
  }

  const otpExists = client.token?.otp;
  if (otpExists && !hasExpired(client.token.expiredAt)) {
    return res.status(200).json({
      status: 'success',
      message: 'Token has already been sent',
      otp: process.env.NODE_ENV === 'development' ? client.token.otp : null
    });
  }

  const otpResponse = await sendOTPToClient(client, OTPSendType.Email);
  if (!otpResponse.success) {
    return res.status(400).json({
      status: 'failed',
      message: otpResponse.message,
      otp: null
    });
  }

  return res.status(200).json({
    status: 'success',
    message: 'OTP sent successfully via email',
    otp: process.env.NODE_ENV === 'development' ? otpResponse.otp : null
  });
});

export const verifyClientOTP = asyncHandler(async (req: Request, res: Response) => {
  const { phoneNumber, otp } = req.body as {
    otp: string;
    phoneNumber: string;
  };

  const { error } = resetDeviceIdApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const { formattedNumber, isValid } = validatePhoneNumber(phoneNumber);
  if (!isValid) {
    return res.status(422).json({ error: `Invalid phone number ${phoneNumber} provided` });
  }

  const client = await Client.findOne({
    $or: [{ phoneNumber: phoneNumber }, { phoneNumber: formattedNumber }],
    isActive: true
  });

  if (!client) {
    return res.status(404).json({
      status: 'failed',
      message: 'No client found with the provided phone number'
    });
  }

  const otpValid = client.token?.otp === otp;
  if (otpValid && !hasExpired(client.token.expiredAt)) {
    const authToken = client.getSignedJwtToken();
    // Remove the token field entirely after successful verification
    await Client.findByIdAndUpdate(client._id, { $unset: { token: 1 } });
    return res.status(200).json({
      status: 'success',
      authToken
    });
  } else {
    return res.status(400).json({
      status: 'failed',
      message: 'Invalid or expired OTP'
    });
  }
});

export const getClients = asyncHandler(async (req: Request, res: Response) => {
  const clients = await advancedResults<ClientDoc, ClientDocumentResult & Document>(req.url, Client);
  return res.status(200).json({
    status: 'success',
    data: clients
  });
});

export const loginWithToken = asyncHandler(async (req: Request & { client: ClientDoc }, res: Response) => {
  const { deviceId, fcmToken } = req.body as RegisterClientRequestBody;
  const client = req.client;
  if (!client) {
    return res.status(401).json({ error: 'Client not authenticated' });
  }

  const updatedClient = await Client.findOneAndUpdate<ClientDocumentResult>(
    { _id: client._id },
    { fcmToken, deviceId },
    { new: true }
  );

  if (!updatedClient) {
    return res.status(404).json({ error: 'Client not found' });
  }

  const authToken = updatedClient.getSignedJwtToken();
  const sanitizedClient = sanitizeClientUser(updatedClient._doc);

  return res.status(200).json({
    status: 'success',
    code: 'AUTH_200',
    message: AUTH_CODES.AUTH_200,
    data: { ...sanitizedClient, fcmToken, authToken }
  });
});
