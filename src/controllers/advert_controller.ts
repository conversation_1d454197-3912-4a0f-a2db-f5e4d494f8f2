import { Request, Response } from 'express';

import { createAdvertApiValidator } from '../api_validators/advert-api-validators.js';
import { createBrandApiValidator } from '../api_validators/brands-api-validators.js';
import { isTodayGreaterThan, isValidDateString } from '../helpers/application_helper.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import Advert, { AdvertDocumentResult } from '../models/AdvertModel/AdvertModel.js';
import { UserDoc } from '../types/user.js';

export const createAdvert = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { carId: string; startDate: string; endDate: string };
  const { carId, startDate, endDate } = body;

  const { error } = createAdvertApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  if (!isValidDateString(startDate) || !isValidDateString(endDate)) {
    return res.status(422).json({ error: 'kindly pass in a valid date' });
  }

  const carAdvert = await Advert.findOneActive({ car: carId });
  if (carAdvert && !isTodayGreaterThan(carAdvert.endDate)) {
    return res.status(422).json({
      error:
        'You already have an advert running for this car until ' +
        new Date(carAdvert.endDate).toLocaleDateString('en-US', {
          weekday: 'short',
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        })
    });
  }

  const user = req.user as UserDoc;
  const companyId = user.company._id;
  const newAdvert = new Advert({
    car: carId,
    company: companyId,
    startDate,
    endDate
  });
  await newAdvert.save();

  return res.status(201).json({
    status: 'success',
    message: 'Advert created Successfully',
    data: newAdvert
  });
});

export const getAdvertById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const advert = await Advert.findOneActive({ _id: id });
  if (!advert) {
    return res.status(404).json({
      status: 'success',
      message: 'Advert not found'
    });
  }

  return res.status(200).json({
    status: 'success',
    data: advert
  });
});

export const updateAdvert = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { carId: string; startDate: string; endDate: string };
  const { startDate, endDate } = body;
  const { id } = req.params;
  const { error } = createBrandApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const advert = await Advert.findOneActive({ _id: id });
  if (!advert) {
    return res.status(404).json({ error: `Brand not found with the id ${id} provided` });
  }

  const updatedBrand = await Advert.findOneAndUpdate<AdvertDocumentResult>(
    { _id: id },
    { startDate, endDate },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'Advert Updated Successfully.',
    data: updatedBrand
  });
});

export const deleteAdvert = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const advert = await Advert.findOneActive({ _id: id });
  if (!advert) {
    return res.status(404).json({ error: `Advert not found with the id ${id} provided` });
  }

  await Advert.findOneAndUpdate<AdvertDocumentResult>({ _id: id }, { isActive: false });

  return res.status(200).json({
    status: 'success',
    message: 'Advert deactivated Successfully.'
  });
});

export const getAdverts = asyncHandler(async (req: Request, res: Response) => {
  // Get current date in ISO format
  const today = new Date().toISOString();

  const pipeline = [
    {
      $match: {
        isActive: true,
        startDate: { $lte: today },
        endDate: { $gte: today }
      }
    },
    {
      $lookup: {
        from: 'cars',
        localField: 'car',
        foreignField: '_id',
        as: 'car',
        pipeline: [
          {
            $lookup: {
              from: 'carimages',
              localField: '_id',
              foreignField: 'carId',
              as: 'carImages',
              pipeline: [
                { $match: { isActive: true, isVisible: true } },
                { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
              ]
            }
          },
          {
            $lookup: {
              from: 'brands',
              localField: 'brand',
              foreignField: '_id',
              as: 'brand'
            }
          },
          {
            $unwind: {
              path: '$brand',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $lookup: {
              from: 'carcategories',
              localField: 'category',
              foreignField: '_id',
              as: 'category'
            }
          },
          {
            $unwind: {
              path: '$category',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $project: {
              _id: 1,
              model: 1,
              year: 1,
              dailyPrice: 1,
              dailyMinPrice: 1,
              carImages: 1,
              brand: '$brand.name',
              category: {
                _id: '$category._id',
                name: '$category.name',
                pictureUrl: '$category.pictureUrl'
              }
            }
          }
        ]
      }
    },
    {
      $unwind: {
        path: '$car',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        startDate: 1,
        endDate: 1,
        isApproved: 1,
        advertType: 1,
        spentBudget: 1,
        targetViews: 1,
        targetClicks: 1,
        totalViews: 1,
        totalClicks: 1,
        budgetCap: 1,
        createdAt: 1,
        updatedAt: 1,
        car: 1,
        company: {
          _id: '$company._id',
          name: '$company.name'
        }
      }
    }
  ];

  const result = await Advert.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: result
  });
});

//  <<<switched from this to the aggregate method above>>>

// export const getAdvert = asyncHandler(async (req: Request, res: Response) => {
//   const today = new Date().toISOString().slice(0, 10);
//   const adverts = await Advert.find({
//     isActive: true,
//     startDate: { $lte: today },
//     endDate: { $gte: today }
//   })
//     .populate({
//       path: 'car',
//       select: 'carImages',
//       match: { 'carImages.isVisible': true },
//       populate: {
//         path: 'carImages',
//         select: 'url isMain carId'
//       }
//     })
//     .populate({
//       path: 'company',
//       select: 'name'
//     })
//     .populate({
//       path: 'rating',
//       select: 'comment rating'
//     });

//   return res.status(200).json({
//     status: 'success',
//     data: adverts
//   });
// });

// export const getAdvert = asyncHandler(async (req: Request, res: Response) => {
//   const today = new Date().toISOString().slice(0, 10);
//   const adverts = await Advert.find({
//     isActive: true,
//     startDate: { $lte: today },
//     endDate: { $gte: today }
//   })
//     .populate({
//       path: 'car',
//       select: '',
//       populate: {
//         path: 'carImages',
//         select: 'url isMain carId isVisible'
//       }
//     })
//     .populate({
//       path: 'company',
//       select: 'name'
//     })
//     .populate({
//       path: 'rating',
//       select: 'comment rating'
//     });

//   return res.status(200).json({
//     status: 'success',
//     data: adverts
//   });
// });
