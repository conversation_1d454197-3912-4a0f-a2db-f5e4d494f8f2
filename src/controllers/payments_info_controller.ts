import { Request, Response } from 'express';

import { createPaymentInfoApiValidator } from '../api_validators/payments-api-validators.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import PaymentInfo from '../models/PaymentInfoModel/PaymentInfoModel.js';
import { RegisterPaymentInfoRequestBody } from '../types/payment_info.js';
import { UserDoc } from '../types/user.js';

export const createPaymentInfo = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterPaymentInfoRequestBody;
  const { accountName, bankName, accountNumber } = body;
  const { error } = createPaymentInfoApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const user = req.user as UserDoc;
  const paymentInfoDoc = {
    accountName,
    bankName,
    accountNumber,
    company: user.company,
    createdBy: user._id,
    updatedBy: user._id
  };
  const newPaymentInfo = new PaymentInfo(paymentInfoDoc);
  await newPaymentInfo.save();

  await Company.findOneAndUpdate(
    { _id: user.company },
    {
      paymentInfo: newPaymentInfo._id
    }
  );

  return res.status(200).json({
    status: 'success',
    data: newPaymentInfo
  });
});

export const getPaymentInfo = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const paymentInfo = await PaymentInfo.findOne({ createdBy: user._id });
  return res.status(200).json({
    status: 'success',
    data: paymentInfo?.accountName ? paymentInfo : 'No payment info found'
  });
});
