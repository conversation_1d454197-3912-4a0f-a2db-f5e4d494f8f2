import { Request, Response } from 'express';
import fs from 'fs';
import mongoose, { Document } from 'mongoose';

import {
  createCarApiValidator,
  deleteCarImagesApiValidator,
  fetchCarsApiValidator,
  updateCarApiValidator,
  updateMainImageApiValidator,
  updateVisibleCarImages
} from '../api_validators/car-api-validators.js';
import { addPointsToAddress } from '../helpers/application_helper.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import { manageCategoryFileUpload, manageFileUpload } from '../helpers/file_upload.js';
import { advancedResults } from '../helpers/query.js';
import Logger from '../libs/logger.js';
import Brand from '../models/BrandModel/BrandModel.js';
import CarCategory, { CarCategoryDocumentResult } from '../models/CarCategoryModel/CarCategoryModel.js';
import CarImage from '../models/CarImageModel/CarImageModel.js';
import Car, { CarDocumentResult } from '../models/CarModel/CarModel.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import { CarDoc, RegisterCarRequestBody, UpdateCarRequestBody } from '../types/car.js';
import { AddressDoc, USER_ROLE, UserDoc } from '../types/user.js';
import { FetchCarsRequestBody } from './../types/car.js';

export const createCategory = asyncHandler(async (req: Request, res: Response) => {
  if (!req.file) {
    return res.status(400).json({
      status: 'failed',
      message: 'Category image is required',
      error: 'NO_IMAGE_PROVIDED'
    });
  }

  const body = req.body as { name: string };
  const { name } = body;

  if (!name || name.trim().length === 0) {
    return res.status(400).json({
      status: 'failed',
      message: 'Category name is required and cannot be empty',
      error: 'INVALID_CATEGORY_NAME'
    });
  }

  try {
    // Check if category with same name already exists
    const existingCategory = await CarCategory.findOne({
      name: name.trim(),
      isActive: true
    });

    if (existingCategory) {
      return res.status(409).json({
        status: 'failed',
        message: 'A category with this name already exists',
        error: 'CATEGORY_ALREADY_EXISTS'
      });
    }

    const newCategory = new CarCategory({ name: name.trim() });
    await newCategory.save();

    // Upload image to Cloudinary
    const { path, filename, originalname } = req.file;
    await manageCategoryFileUpload(path, filename, originalname, newCategory._id.toString());

    Logger.info(`New category created: ${newCategory.name} (ID: ${newCategory._id.toString()})`);

    return res.status(201).json({
      status: 'success',
      message: 'Category created successfully. Image is being processed.',
      data: {
        ...newCategory.toObject(),
        imageProcessing: true,
        estimatedProcessingTime: '30-60 seconds'
      }
    });
  } catch (error) {
    Logger.error('Error creating category:', error);

    return res.status(500).json({
      status: 'failed',
      message: 'Failed to create category. Please try again.',
      error: 'CATEGORY_CREATION_ERROR'
    });
  }
});

export const getBrandsAndCategories = asyncHandler(async (_req: Request, res: Response) => {
  const categories = await CarCategory.find({});
  const brands = await Brand.find({});
  return res.status(200).json({
    status: 'success',
    data: { categories, brands }
  });
});

export const updateCarCategory = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { name: string };
  const { id } = req.params;
  const { name } = body;

  if (!name) {
    return res.status(422).json({ error: 'Category name is required' });
  }

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const category = await CarCategory.findOneActive({ _id: id });
  if (!category) {
    return res.status(404).json({ error: `Category not found with the id ${id} provided` });
  }

  const updatedCategory = await CarCategory.findOneAndUpdate<CarCategoryDocumentResult>(
    { _id: id },
    { name },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'Category Updated Successfully.',
    data: updatedCategory
  });
});

export const deleteCarCategory = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const category = await CarCategory.findOneActive({ _id: id });
  if (!category) {
    return res.status(404).json({ error: `category not found with the id ${id} provided` });
  }

  const inactiveCategory = await CarCategory.findOneAndUpdate<CarCategoryDocumentResult>(
    { _id: id },
    { isActive: false },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'Category deactivated Successfully.',
    data: inactiveCategory
  });
});

export const createCar = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterCarRequestBody;
  const { year, brandId, model, categoryId, address, dailyPrice, dailyMinPrice, schedule } = body;

  const { error } = createCarApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const user = req.user as UserDoc;

  const company = await Company.findOne({ user: user._id });
  const updateAddress = addPointsToAddress(address);

  const newCar = new Car({
    year,
    brand: brandId,
    model,
    category: categoryId,
    address: updateAddress,
    dailyPrice,
    dailyMinPrice,
    schedule,
    createdBy: user._id,
    company: company._id
  });
  await newCar.save();
  await newCar.populate({ path: 'brand', select: 'name' });
  await newCar.populate({ path: 'category', select: 'name thumbnailUrl mediumUrl bigUrl' });

  return res.status(201).json({
    status: 'success',
    message: 'New Car created Successfully',
    data: newCar
  });
});

export const getCars = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const company = user.company.toString();

  // Create a new URL with the createdBy parameter
  const url = new URL(req.url, 'http://localhost');
  url.searchParams.set('createdBy', user._id.toString());

  const cars = await advancedResults<CarDoc, CarDocumentResult & Document>(url.toString(), Car, company);

  // Use aggregation to include bookingCount and totalEarned for each car
  const carIds = cars.results.map(car => car._id);

  const pipeline = [
    {
      $match: {
        _id: { $in: carIds },
        isActive: true
      }
    },
    {
      $lookup: {
        from: 'carimages',
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } },
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'carcategories',
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        description: 1,
        engineType: 1,
        schedule: 1,
        address: 1,
        year: 1,
        dailyPrice: 1,
        dailyMinPrice: 1,
        model: 1,
        ratingSummary: 1,
        brand: '$brand.name',
        category: {
          _id: '$category._id',
          name: '$category.name',
          thumbnailUrl: '$category.thumbnailUrl',
          mediumUrl: '$category.mediumUrl',
          bigUrl: '$category.bigUrl'
        },
        carImages: 1,
        bookingCount: 1,
        totalEarned: 1,
        bookedDates: 1,
        isAvailable: 1,
        createdAt: 1,
        updatedAt: 1
      }
    }
  ];

  const enrichedCars = await Car.aggregate(pipeline);

  // Maintain the same structure as advancedResults
  const result = {
    ...cars,
    results: enrichedCars
  };

  return res.status(200).json({
    status: 'success',
    data: result
  });
});

export const uploadCarImages = asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  Logger.info('Car image upload request started', {
    userId: req.user?._id,
    timestamp: new Date().toISOString()
  });

  try {
    // Validate file upload
    if (!req.files || (Array.isArray(req.files) && req.files.length === 0)) {
      Logger.warn('No files uploaded in request');
      return res.status(400).json({
        status: 'failed',
        message: 'Please upload at least one image file.',
        error: 'NO_FILES_UPLOADED'
      });
    }

    // Validate request body
    const { carId, mainImageId } = req.body as { carId: string; mainImageId: string };
    if (!carId) {
      Logger.warn('Missing carId in request body');
      return res.status(400).json({
        status: 'failed',
        message: 'Car ID is required.',
        error: 'MISSING_CAR_ID'
      });
    }

    const user = req.user as UserDoc;
    Logger.info(`Processing image upload for car: ${carId} by user: ${user._id.toString()}`);

    // Verify car exists and user has access
    const car = await Car.findOne({ _id: carId });
    if (!car) {
      Logger.warn(`Car not found: ${carId}`);
      return res.status(404).json({
        status: 'failed',
        message: 'Car not found.',
        error: 'CAR_NOT_FOUND'
      });
    }

    // Check if user has permission to upload images for this car
    if (user.role !== USER_ROLE.ADMIN && car.company.toString() !== user.company?.toString()) {
      Logger.warn(`Insufficient permissions for user ${user._id.toString()} to upload images for car ${carId}`);
      return res.status(403).json({
        status: 'failed',
        message: 'You do not have permission to upload images for this car.',
        error: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    const fileData = {
      mainImageId,
      _id: car._id,
      company: car.company,
      createdBy: user._id
    };

    const files = Array.isArray(req.files) ? req.files : req.file ? [req.file] : [];
    Logger.info(`Processing ${files.length} files for upload`);

    // Validate all files before processing
    const validFiles = [];
    for (const file of files) {
      const { path, filename, originalname, size } = file;

      // Validate file properties
      if (!path || !filename || !originalname) {
        Logger.error('Invalid file data:', { path, filename, originalname });
        continue;
      }

      // Check file size (additional validation)
      if (size > 5 * 1024 * 1024) {
        // 5MB limit
        Logger.warn(`File too large: ${originalname} (${size} bytes)`);
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      Logger.warn('No valid files to process');
      return res.status(400).json({
        status: 'failed',
        message: 'No valid files to process.',
        error: 'NO_VALID_FILES'
      });
    }

    const uploadPromises: Promise<void>[] = [];

    // Process all valid files
    for (const file of validFiles) {
      const { path, filename, originalname } = file;
      Logger.info(`Scheduling upload for file: ${originalname}`);
      uploadPromises.push(manageFileUpload(path, filename, originalname, fileData));
    }

    // Wait for all uploads to be scheduled with timeout
    const scheduleTimeout = 30000; // 30 seconds timeout for scheduling
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Timeout while scheduling upload jobs'));
      }, scheduleTimeout);
    });

    await Promise.race([Promise.all(uploadPromises), timeoutPromise]);

    const processingTime = Date.now() - startTime;
    Logger.info(`Successfully scheduled ${validFiles.length} image uploads for car: ${carId} in ${processingTime}ms`);

    return res.status(200).json({
      status: 'success',
      message: `${validFiles.length} image(s) are being processed and will be available shortly.`,
      data: {
        carId: car._id,
        filesProcessing: validFiles.length,
        estimatedProcessingTime: '30-60 seconds',
        processingTimeMs: processingTime
      }
    });
  } catch (error) {
    const processingTime = Date.now() - startTime;
    Logger.error('Error in uploadCarImages:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTimeMs: processingTime
    });

    // Clean up any uploaded files on error
    if (req.files && Array.isArray(req.files)) {
      for (const file of req.files) {
        try {
          if (file.path && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
            Logger.info(`Cleaned up file after error: ${file.path}`);
          }
        } catch (cleanupError) {
          Logger.error('Failed to cleanup file after error:', cleanupError);
        }
      }
    }

    return res.status(500).json({
      status: 'failed',
      message: 'Failed to process image uploads. Please try again.',
      error: 'UPLOAD_PROCESSING_ERROR',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export const updateImageVisibility = asyncHandler(async (req: Request, res: Response) => {
  const { error } = updateVisibleCarImages.validate(req.body);
  if (error) {
    return res.status(422).json({
      status: 'failed',
      message: 'Validation error',
      error: error.details[0].message
    });
  }

  const body = req.body as { imageId: string; isVisible: boolean }[];
  const user = req.user as UserDoc;

  if (!body || !Array.isArray(body) || body.length === 0) {
    return res.status(400).json({
      status: 'failed',
      message: 'No image updates provided. Please provide an array of image updates.',
      error: 'NO_UPDATES_PROVIDED'
    });
  }

  try {
    // Validate that all images belong to the user's company
    const imageIds = body.map(item => item.imageId);
    const existingImages = await CarImage.find({
      _id: { $in: imageIds },
      companyId: user.company,
      isActive: true
    });

    if (existingImages.length !== imageIds.length) {
      return res.status(403).json({
        status: 'failed',
        message: 'Some images not found or you do not have permission to update them.',
        error: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    // Update all images in parallel
    const updatePromises = body.map(element =>
      CarImage.findOneAndUpdate(
        { _id: element.imageId, companyId: user.company },
        { isVisible: element.isVisible, updatedBy: user._id },
        { new: true }
      )
    );

    const updatedImages = await Promise.all(updatePromises);

    Logger.info(`Updated visibility for ${updatedImages.length} images by user: ${user._id.toString()}`);

    return res.status(200).json({
      status: 'success',
      message: `Successfully updated visibility for ${updatedImages.length} image(s).`,
      data: {
        updatedCount: updatedImages.length,
        images: updatedImages.map(img => ({
          id: img._id,
          isVisible: img.isVisible
        }))
      }
    });
  } catch (error) {
    Logger.error('Error updating image visibility:', error);

    return res.status(500).json({
      status: 'failed',
      message: 'Failed to update image visibility. Please try again.',
      error: 'UPDATE_ERROR'
    });
  }
});

export const getCar = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({
      status: 'failed',
      message: 'Car ID is required'
    });
  }

  const pipeline = [
    {
      $match: {
        _id: new mongoose.Types.ObjectId(id),
        isActive: true
      }
    },
    {
      $lookup: {
        from: 'carimages',
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } },
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'ratings',
        localField: 'rating',
        foreignField: '_id',
        as: 'rating'
      }
    },
    {
      $unwind: {
        path: '$rating',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $addFields: {
        category: {
          $cond: {
            if: { $not: { $isArray: '$category' } },
            then: { $toObjectId: '$category' },
            else: '$category'
          }
        }
      }
    },
    {
      $lookup: {
        from: 'carcategories',
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        description: 1,
        engineType: 1,
        schedule: 1,
        address: 1,
        year: 1,
        dailyPrice: 1,
        dailyMinPrice: 1,
        model: 1,
        ratingSummary: 1,
        brand: '$brand.name',
        company: '$company.name',
        rating: {
          rating: '$rating.rating',
          comment: '$rating.comment'
        },
        carImages: 1,
        bookingCount: 1,
        totalEarned: 1,
        bookedDates: 1,
        isAvailable: 1,
        createdAt: 1,
        updatedAt: 1,
        category: {
          _id: '$category._id',
          name: '$category.name',
          thumbnailUrl: '$category.thumbnailUrl',
          mediumUrl: '$category.mediumUrl',
          bigUrl: '$category.bigUrl'
        }
      }
    }
  ];

  const result = await Car.aggregate(pipeline);

  if (!result.length) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  return res.status(200).json({
    status: 'success',
    data: result[0]
  });
});

//  <<<switched from this to the aggregate method above>>>

// export const getCar = asyncHandler(async (req: Request, res: Response) => {
//   const { id } = req.params;
//   const user = req.user as UserDoc;
//   const car = await Car.findOneActive({ _id: id, company: user.company });
//   if (!car) {
//     return res.status(404).json({
//       status: 'failed',
//       message: 'car not found'
//     });
//   }
//   await Car.populate(car, {
//     path: 'carImages',
//     match: { isActive: true, isVisible: true },
//     select: 'url isMain carId isVisible'
//   });
//   await Car.populate(car, { path: 'brand', select: 'name' });
//   await Car.populate(car, { path: 'category', select: 'name pictureUrl' });
//   await Car.populate(car, { path: 'company', select: 'name' });
//   await Car.populate(car, { path: 'rating', select: 'comment rating' });

//   return res.status(200).json({
//     status: 'success',
//     data: car
//   });
// });

export const updateCar = asyncHandler(async (req: Request, res: Response) => {
  const { error } = updateCarApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const { id } = req.params;
  const user = req.user as UserDoc;
  const body = req.body as UpdateCarRequestBody;
  const { description, address, dailyPrice, dailyMinPrice, schedule, isAvailable } = body;

  if (!id) {
    return res.status(400).json({
      status: 'failed',
      message: 'Car ID is required'
    });
  }

  const car = await Car.findOneActive({ _id: id });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  const updateAddress: AddressDoc = address?.longitude ? addPointsToAddress(address) : undefined;

  const updatedCar = await Car.findOneAndUpdate(
    { _id: id },
    {
      description,
      address: updateAddress,
      dailyPrice,
      dailyMinPrice,
      schedule,
      isAvailable,
      updatedBy: user._id
    },
    { new: true }
  );

  const pipeline = [
    {
      $match: {
        _id: updatedCar._id
      }
    },
    {
      $lookup: {
        from: 'carimages',
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } },
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'carcategories',
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        description: 1,
        engineType: 1,
        schedule: 1,
        address: 1,
        year: 1,
        dailyPrice: 1,
        dailyMinPrice: 1,
        model: 1,
        brand: '$brand.name',
        category: {
          _id: '$category._id',
          name: '$category.name',
          thumbnailUrl: '$category.thumbnailUrl',
          mediumUrl: '$category.mediumUrl',
          bigUrl: '$category.bigUrl'
        },
        carImages: 1,
        bookingCount: 1,
        totalEarned: 1,
        isAvailable: 1,
        createdAt: 1,
        updatedAt: 1
      }
    }
  ];

  const result = await Car.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: result[0]
  });
});

//  <<<switched from this to the aggregate method above>>>

// export const updateCar = asyncHandler(async (req: Request, res: Response) => {
//   const { error } = updateCarApiValidator.validate(req.body);
//   if (error) {
//     return res.status(422).json({ error: error.details[0].message });
//   }
//   const { id } = req.params;
//   const user = req.user as UserDoc;
//   const body = req.body as UpdateCarRequestBody;
//   const { description, address, dailyPrice, dailyMinPrice, schedule, isAvailable } = body;
//   const car = await Car.findOneActive({ _id: id });
//   if (!car) {
//     return res.status(404).json({
//       status: 'failed',
//       message: 'car not found'
//     });
//   }

//   const updateAddress: AddressDoc = address?.longitude ? addPointsToAddress(address) : undefined;

//   const updatedCar = await Car.findOneAndUpdate(
//     { _id: id },
//     {
//       description,
//       address: updateAddress,
//       dailyPrice,
//       dailyMinPrice,
//       schedule,
//       isAvailable,
//       updatedBy: user._id
//     },
//     { new: true }
//   );

//   await Car.populate(updatedCar, {
//     path: 'carImages',
//     match: { isVisible: true },
//     select: 'url isMain carId isVisible'
//   });
//   await Car.populate(updatedCar, { path: 'brand', select: 'name' });
//   await Car.populate(updatedCar, { path: 'category', select: 'name pictureUrl' });
//   await Car.populate(updatedCar, { path: 'company', select: 'name' });

//   return res.status(200).json({
//     status: 'success',
//     data: updatedCar
//   });
// });

export const delistCar = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const car = await Car.findActive({ _id: id });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'car not found'
    });
  }

  await Car.findOneAndUpdate(
    { _id: id },
    {
      isActive: false
    }
  );

  return res.status(200).json({
    status: 'success',
    message: 'car delisted successfully'
  });
});

export const deleteCarImages = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userCompanpany = req.user as UserDoc;
  const company = userCompanpany.company;
  const car = await Car.findActive({ _id: id, company });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'car not found'
    });
  }
  const { error } = deleteCarImagesApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const body = req.body as { imageIds: string[] };
  const user = req.user as UserDoc;

  if (!body.imageIds.length) {
    return res.status(404).json({
      status: 'failed',
      message: 'No Car Images to Update.'
    });
  }

  body.imageIds.forEach(async id => {
    await CarImage.findOneAndUpdate(
      { _id: id, companyId: user.company },
      {
        isActive: false
      }
    );
  });

  return res.status(200).json({
    status: 'success',
    message: 'Car Images Deleted Successfully'
  });
});

export const updateMainImage = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const user = req.user as UserDoc;
  const { company } = user;
  const body = req.body as { imageId?: string };

  const car = await Car.findActive({ _id: id, company });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  const { error } = updateMainImageApiValidator.validate(body);
  if (error) {
    return res.status(422).json({
      status: 'failed',
      message: error.details[0].message
    });
  }

  if (!body.imageId) {
    return res.status(400).json({
      status: 'failed',
      message: 'Image ID is required to update the car image.'
    });
  }

  const mainImage = await CarImage.findOne({ isMain: true, companyId: company });

  const updateTasks: Promise<unknown>[] = [];

  if (mainImage) {
    updateTasks.push(CarImage.findOneAndUpdate({ _id: mainImage._id, companyId: company }, { isMain: false }));
  }

  updateTasks.push(CarImage.findOneAndUpdate({ _id: body.imageId, companyId: company }, { isMain: true }));
  await Promise.all(updateTasks);

  return res.status(200).json({
    status: 'success',
    message: 'Car image updated successfully.'
  });
});

export const fetchCarsForClient = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as FetchCarsRequestBody;
  const { error } = fetchCarsApiValidator.validate(body);
  if (error) {
    return res.status(422).json({
      status: 'failed',
      message: error.details[0].message
    });
  }

  // Extract pickup address data for enhanced filtering
  const { state: pickupState, longitude: pickupLongitude, latitude: pickupLatitude } = body.pickupAddress;
  const proximityRadiusKm = 20; // 20km radius for cross-state cars

  Logger.info(`Fetching cars for pickup state: ${pickupState} at coordinates: [${pickupLongitude}, ${pickupLatitude}]`);
  Logger.info(`Including cars within ${proximityRadiusKm}km radius from other states`);

  // Optional schedule filtering based on current time
  const { useScheduleFilter = true } = body;
  let scheduleMatchCondition = {};

  if (useScheduleFilter) {
    // Determine current time in the specified timezone
    const now = new Date();
    const currentHour = now.getHours(); // You might want to use a timezone library for more accuracy

    // Define schedule availability based on time
    // DAY: 3 AM to 3 PM, NIGHT: 3 PM to 3 AM, DAY_NIGHT: Always available
    const availableSchedules = ['Day/Night']; // Always include DAY_NIGHT cars

    if (currentHour >= 3 && currentHour < 15) {
      // Daytime: 3 AM to 3 PM
      availableSchedules.push('Day');
    } else {
      // Nighttime: 3 PM to 3 AM
      availableSchedules.push('Night');
    }

    scheduleMatchCondition = { schedule: { $in: availableSchedules } };
    Logger.info(
      `Applying schedule filter for time ${currentHour}:00. Available schedules: ${availableSchedules.join(', ')}`
    );
  }

  const pipeline = [
    {
      // Step 1: Enhanced matching - cars in same state OR within 20km radius from other states
      $match: {
        isActive: true,
        $or: [
          // Primary: All cars in the same state as pickup address
          {
            'address.state': pickupState,
            ...scheduleMatchCondition
          },
          // Secondary: Cars from other states within 20km radius
          {
            'address.state': { $ne: pickupState }, // Different state
            'address.location': {
              $geoWithin: {
                $centerSphere: [[pickupLongitude, pickupLatitude], proximityRadiusKm / 111.32] // Approximate km to degrees conversion
              }
            },
            ...scheduleMatchCondition
          }
        ]
      }
    },
    {
      // Step 1.5: Add distance calculations and source type for sorting and display
      $addFields: {
        // Distance from car to pickup location
        carToPickupDistanceKm: {
          $multiply: [
            {
              $acos: {
                $add: [
                  {
                    $multiply: [
                      { $sin: { $degreesToRadians: pickupLatitude } },
                      { $sin: { $degreesToRadians: { $arrayElemAt: ['$address.location.coordinates', 1] } } }
                    ]
                  },
                  {
                    $multiply: [
                      { $cos: { $degreesToRadians: pickupLatitude } },
                      { $cos: { $degreesToRadians: { $arrayElemAt: ['$address.location.coordinates', 1] } } },
                      {
                        $cos: {
                          $degreesToRadians: {
                            $subtract: [{ $arrayElemAt: ['$address.location.coordinates', 0] }, pickupLongitude]
                          }
                        }
                      }
                    ]
                  }
                ]
              }
            },
            6371 // Earth's radius in kilometers
          ]
        },
        // Distance from pickup to destination
        pickupToDestinationDistanceKm: {
          $multiply: [
            {
              $acos: {
                $add: [
                  {
                    $multiply: [
                      { $sin: { $degreesToRadians: pickupLatitude } },
                      { $sin: { $degreesToRadians: body.destinationAddress.latitude } }
                    ]
                  },
                  {
                    $multiply: [
                      { $cos: { $degreesToRadians: pickupLatitude } },
                      { $cos: { $degreesToRadians: body.destinationAddress.latitude } },
                      {
                        $cos: {
                          $degreesToRadians: {
                            $subtract: [body.destinationAddress.longitude, pickupLongitude]
                          }
                        }
                      }
                    ]
                  }
                ]
              }
            },
            6371 // Earth's radius in kilometers
          ]
        },
        sourceType: {
          $cond: {
            if: { $eq: ['$address.state', pickupState] },
            then: 'same_state',
            else: 'nearby_state'
          }
        }
      }
    },
    {
      // Step 1.6: Calculate total distance
      $addFields: {
        totalDistanceKm: {
          $add: ['$carToPickupDistanceKm', '$pickupToDestinationDistanceKm']
        },
        // Keep distanceFromPickup for backward compatibility
        distanceFromPickup: '$carToPickupDistanceKm'
      }
    },
    {
      // Step 2: Populate carImages for each car
      $lookup: {
        from: 'carimages', // Collection name for car images
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } }, // Only active and visible car images
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },

    {
      // Step 3: Populate brand field
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true // If no brand, leave it as null
      }
    },
    {
      // Step 4: Populate company field
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true // If no company, leave it as null
      }
    },
    {
      $lookup: {
        from: 'ratings',
        localField: 'rating',
        foreignField: '_id',
        as: 'rating'
      }
    },
    {
      $unwind: {
        path: '$rating',
        preserveNullAndEmptyArrays: true // If no company, leave it as null
      }
    },
    {
      // Step 5: Cast category field to ObjectId if necessary
      $addFields: {
        category: {
          $cond: {
            if: { $not: { $isArray: '$category' } },
            then: { $toObjectId: '$category' }, // Cast to ObjectId if it's not an array
            else: '$category'
          }
        }
      }
    },
    {
      // Step 6: Populate category field
      $lookup: {
        from: 'carcategories', // Collection name for car categories
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true // If no category, leave it as null
      }
    },
    {
      // Step 7: Group cars by their category
      $group: {
        _id: '$category._id',
        name: { $first: '$category.name' },
        thumbnailUrl: { $first: '$category.thumbnailUrl' },
        mediumUrl: { $first: '$category.mediumUrl' },
        bigUrl: { $first: '$category.bigUrl' },
        createdAt: { $first: '$category.createdAt' },
        cars: {
          $push: {
            _id: '$_id',
            description: '$description',
            engineType: '$engineType',
            schedule: '$schedule',
            address: '$address',
            year: '$year',
            dailyPrice: '$dailyPrice',
            dailyMinPrice: '$dailyMinPrice',
            model: '$model',
            ratingSummary: '$ratingSummary',
            brand: '$brand.name',
            company: '$company.name',
            rating: {
              rating: '$rating.rating',
              comment: '$rating.comment'
            },
            carImages: '$carImages',
            bookingCount: '$bookingCount',
            totalEarned: '$totalEarned',
            bookedDates: '$bookedDates',
            isAvailable: '$isAvailable',
            carToPickupDistanceKm: '$carToPickupDistanceKm', // Distance from car to pickup
            pickupToDestinationDistanceKm: '$pickupToDestinationDistanceKm', // Distance from pickup to destination
            totalDistanceKm: '$totalDistanceKm', // Total distance
            distanceFromPickup: '$distanceFromPickup', // Backward compatibility
            sourceType: '$sourceType', // 'same_state' or 'nearby_state'
            createdAt: '$createdAt',
            updatedAt: '$updatedAt'
          }
        }
      }
    },
    {
      // Step 8: Sort cars within each category by source type and distance
      $addFields: {
        cars: {
          $map: {
            input: {
              $sortArray: {
                input: '$cars',
                sortBy: {
                  sourceType: 1, // 'nearby_state' comes before 'same_state' alphabetically, so we want reverse
                  distanceFromPickup: 1 // Closest cars first
                }
              }
            },
            as: 'car',
            in: {
              $mergeObjects: [
                '$$car',
                {
                  // Round distances to 2 decimal places for better readability
                  carToPickupDistanceKm: {
                    $round: ['$$car.carToPickupDistanceKm', 2]
                  },
                  pickupToDestinationDistanceKm: {
                    $round: ['$$car.pickupToDestinationDistanceKm', 2]
                  },
                  totalDistanceKm: {
                    $round: ['$$car.totalDistanceKm', 2]
                  },
                  distanceFromPickup: {
                    $round: ['$$car.distanceFromPickup', 2]
                  }
                }
              ]
            }
          }
        }
      }
    },
    {
      // Step 9: Sort cars within each category properly (same_state first, then by distance)
      $addFields: {
        cars: {
          $concatArrays: [
            // First: same_state cars sorted by distance
            {
              $sortArray: {
                input: {
                  $filter: {
                    input: '$cars',
                    cond: { $eq: ['$$this.sourceType', 'same_state'] }
                  }
                },
                sortBy: { carToPickupDistanceKm: 1 }
              }
            },
            // Then: nearby_state cars sorted by distance
            {
              $sortArray: {
                input: {
                  $filter: {
                    input: '$cars',
                    cond: { $eq: ['$$this.sourceType', 'nearby_state'] }
                  }
                },
                sortBy: { carToPickupDistanceKm: 1 }
              }
            }
          ]
        }
      }
    },
    {
      // Step 10: Format the final output
      $project: {
        id: '$_id',
        name: 1,
        imageUrl: 1,
        createdAt: 1,
        cars: 1
      }
    }
  ];

  // Execute the aggregation pipeline
  const result = await Car.aggregate(pipeline);

  // Enhanced logging with detailed breakdown
  const totalCategories = result.length;
  const totalCars = result.reduce((sum: number, category: any): number => {
    return sum + (Array.isArray(category.cars) ? category.cars.length : 0);
  }, 0);

  // Count cars by source type for detailed logging
  let sameStateCars = 0;
  let nearbyStateCars = 0;

  result.forEach((category: any) => {
    category.cars.forEach((car: { sourceType: string }) => {
      if (car.sourceType === 'same_state') {
        sameStateCars++;
      } else {
        nearbyStateCars++;
      }
    });
  });

  Logger.info(`Enhanced car search results for state: ${pickupState}`);
  Logger.info(`- Total categories: ${totalCategories}`);
  Logger.info(`- Total cars: ${totalCars}`);
  Logger.info(`- Same state cars (${pickupState}): ${sameStateCars}`);
  Logger.info(`- Nearby state cars (within ${proximityRadiusKm}km): ${nearbyStateCars}`);

  return res.status(200).json({
    status: 'success',
    message: `Found ${totalCategories} categories with ${totalCars} cars (${sameStateCars} in ${pickupState}, ${nearbyStateCars} nearby)`,
    data: result,
    meta: {
      totalCategories,
      totalCars,
      sameStateCars,
      nearbyStateCars,
      searchRadius: `${proximityRadiusKm}km`,
      pickupState
    }
  });
});
