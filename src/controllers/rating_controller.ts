import { Request, Response } from 'express';

import { createRatingApiValidator } from '../api_validators/rating-api-validators.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import { calculateRatingSummary } from '../helpers/car_helper.js';
import Car from '../models/CarModel/CarModel.js';
import Rating from '../models/RatingModel/RatingModel.js';

export const createRating = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { carId: string; comment: string; rating: number };
  const { carId, rating, comment } = body;

  const { error } = createRatingApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const client = req.client;
  const newRating = new Rating({ carId, rating, comment, clientId: client._id });
  await newRating.save();

  if (newRating) {
    const averageRating = await calculateRatingSummary(carId);
    await Car.findOneAndUpdate({ _id: carId }, { $push: { ratings: newRating._id } }, { ratingSummary: averageRating });
  }

  return res.status(201).json({
    status: 'success',
    message: 'Rating created Successfully',
    data: newRating
  });
});

export const deleteRating = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const rating = await Rating.findOne({ _id: id });
  if (!rating) {
    return res.status(404).json({ error: `Rating not found with the id ${id} provided` });
  }

  await Rating.deleteOne({ _id: id });

  return res.status(200).json({
    status: 'success',
    message: 'Rating deleted Successfully.'
  });
});
