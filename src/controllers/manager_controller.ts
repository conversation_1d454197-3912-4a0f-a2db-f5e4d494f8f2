import { Request, Response } from 'express';

import { createManagerApiValidator } from '../api_validators/manager-api-validator.js';
import { createClientApiValidator } from '../api_validators/users-api-validators.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import { sendWelcomeEmail } from '../helpers/user_helper.js';
import Manager, { ManagerDocumentResult } from '../models/ManagerModel/ManagerModel.js';
import User from '../models/UserModel/UserModel.js';
import { RegisterManagerRequestBody } from '../types/manager.js';
import { USER_ROLE } from '../types/user.js';

export const createManager = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterManagerRequestBody;
  const { companyName, supportEmail, supportPhone, currency, address } = body;
  const { password, deviceId } = req.body as { password: string; deviceId: string };
  const { error } = createClientApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const userDoc = {
    email: supportEmail,
    phone_number: supportEmail,
    first_name: companyName,
    last_name: companyName,
    password,
    deviceId,
    role: USER_ROLE.MANAGER
  };
  const newUser = new User(userDoc);
  await newUser.save();

  if (newUser) {
    const manager = new Manager({
      companyName,
      supportEmail,
      supportPhone,
      user: newUser._id,
      currency,
      address
    });
    await manager.save();

    if (manager) {
      await sendWelcomeEmail(newUser.toJSON());
      return res.status(201).json({
        status: 'true',
        message: 'Manager Created successfully',
        data: newUser
      });
    }
  }
});

export const getManager = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const manager = await Manager.findOne<ManagerDocumentResult>({ _id: id });
  if (!manager) {
    return res.status(404).json({
      status: 'failed',
      message: `Manager not found with id ${id}`
    });
  }

  return res.status(200).json({
    status: 'success',
    data: manager
  });
});

export const updateManager = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const body = req.body as RegisterManagerRequestBody;
  const { companyName, supportEmail, supportPhone, currency, address } = body;

  const { error } = createManagerApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const manager = await User.findOneActive({ _id: id });
  if (!manager) {
    return res.status(404).json({ error: `user not found with the id ${id} provided` });
  }

  const updatedManager = await Manager.findOneAndUpdate<ManagerDocumentResult>(
    { _id: id },
    { companyName, supportEmail, supportPhone, currency, address },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'User Updated Successfully.',
    data: updatedManager
  });
});
