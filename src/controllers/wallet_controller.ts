import { Request, Response } from 'express';

import { asyncHandler } from '../helpers/asyncHandler.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import { WalletService } from '../services/wallet_service.js';
import { UserDoc } from '../types/user.js';

export const getWalletBalance = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const company = await Company.findOne({ _id: user.company });
  if (!company) {
    return res.status(404).json({ status: 'failed', message: 'Company not found' });
  }

  try {
    // Ensure wallet exists for the company
    await WalletService.ensureWalletExists(company._id, user._id);

    // Get comprehensive wallet balance
    const walletBalance = await WalletService.getWalletBalance(company._id);

    return res.status(200).json({
      status: 'success',
      data: walletBalance
    });
  } catch (error) {
    return res.status(500).json({
      status: 'failed',
      message: 'Error fetching wallet balance',
      error: error.message
    });
  }
});

export const getWalletTransactions = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const { limit = 50, skip = 0 } = req.query;

  const company = await Company.findOne({ _id: user.company });
  if (!company) {
    return res.status(404).json({ status: 'failed', message: 'Company not found' });
  }

  try {
    const transactions = await WalletService.getWalletTransactions(company._id, Number(limit), Number(skip));

    return res.status(200).json({
      status: 'success',
      data: transactions
    });
  } catch (error) {
    return res.status(500).json({
      status: 'failed',
      message: 'Error fetching wallet transactions',
      error: error.message
    });
  }
});

export const reconcileWallet = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const company = await Company.findOne({ _id: user.company });
  if (!company) {
    return res.status(404).json({ status: 'failed', message: 'Company not found' });
  }

  try {
    const reconciledWallet = await WalletService.reconcileWallet(company._id);

    return res.status(200).json({
      status: 'success',
      message: 'Wallet reconciled successfully',
      data: reconciledWallet
    });
  } catch (error) {
    return res.status(500).json({
      status: 'failed',
      message: 'Error reconciling wallet',
      error: error.message
    });
  }
});

export const validateWalletConsistency = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const company = await Company.findOne({ _id: user.company });
  if (!company) {
    return res.status(404).json({ status: 'failed', message: 'Company not found' });
  }

  try {
    const validation = await WalletService.validateWalletConsistency(company._id);

    return res.status(200).json({
      status: 'success',
      data: validation
    });
  } catch (error) {
    return res.status(500).json({
      status: 'failed',
      message: 'Error validating wallet consistency',
      error: error.message
    });
  }
});
