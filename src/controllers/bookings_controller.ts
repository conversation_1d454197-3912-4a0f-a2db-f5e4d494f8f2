import { Request, Response } from 'express';
import { Document } from 'mongoose';

import { createBookingApiValidator } from '../api_validators/booking-api-validator.js';
import { addPointsToAddress } from '../helpers/application_helper.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import { sendCanceledBookingEmail } from '../helpers/booking_helper.js';
import { triggerEmail } from '../helpers/mail_helper.js';
import { sendNotification } from '../helpers/notification_helper.js';
import { advancedResults } from '../helpers/query.js';
import Booking, { BookingDocumentResult } from '../models/BookingModel/BookingModel.js';
import Car from '../models/CarModel/CarModel.js';
import Client from '../models/ClientModel/ClientModel.js';
import Transaction from '../models/TransactionModel/TransactionModel.js';
import User from '../models/UserModel/UserModel.js';
import { WalletService } from '../services/wallet_service.js';
import {
  APPROVAL_STATUS,
  BOOKING_STATUS,
  BOOKING_TYPE,
  BookingDoc,
  BookingDocExt,
  RegisterBookingRequestBody
} from '../types/booking.js';
import { TRANSACTION_TYPE } from '../types/transactions.js';
import { USER_ROLE, UserDoc } from '../types/user.js';

export const createBooking = asyncHandler(async (req: Request, res: Response) => {
  const { error } = createBookingApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const body = req.body as RegisterBookingRequestBody;
  const {
    carId,
    startDate,
    endDate,
    startTime,
    pickupAddress,
    destinationAddress,
    escortCount,
    escortDays,
    hasAdditionalStop
  } = body;

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (new Date(startDate) < today) {
    return res.status(422).json({
      status: 'failed',
      message: 'Start date cannot be before today'
    });
  }

  if (new Date(endDate) < new Date(startDate)) {
    return res.status(422).json({
      status: 'failed',
      message: 'End date cannot be before start date'
    });
  }

  const client = req.client;
  const clientId = client._id;
  const car = await Car.findOne({ _id: carId });

  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  const bookingCount = car.bookingCount || 0;

  const newBooking = new Booking({
    car: carId,
    clientId,
    companyId: car.company,
    managerId: car.createdBy,
    startDate,
    endDate,
    hasAdditionalStop,
    bookingCount,
    startTime,
    bookingType: BOOKING_TYPE.STANDARD,
    status: BOOKING_STATUS.PENDING,
    approvalStatus: APPROVAL_STATUS.PENDING,
    pickupAddress: addPointsToAddress(pickupAddress),
    destinationAddress: addPointsToAddress(destinationAddress),
    escortCount,
    escortDays
  });

  const booking = (await newBooking.save()).toJSON();
  delete booking.isActive;
  delete booking.companyId;

  const result = await Booking.populate<BookingDocExt>(booking, {
    path: 'car',
    select: '-_id -isActive',
    populate: [
      { path: 'carImages', select: '-_id -isActive' },
      { path: 'brand', select: 'name -_id' },
      { path: 'category', select: 'name -_id' }
    ]
  });

  const newTransaction = new Transaction({
    clientId: client._id,
    description: TRANSACTION_TYPE.BOOKING + ' transaction',
    bookingId: booking._id,
    reference: booking._id,
    email: client.email,
    currency: 'NGN',
    amount: booking.totalPrice,
    type: TRANSACTION_TYPE.BOOKING
  });
  await newTransaction.save();

  return res.status(201).json({
    status: 'success',
    message: 'Booking Created Successfully',
    data: result
  });
});

export const getRequesterBookings = asyncHandler(async (req: Request, res: Response) => {
  // Create a new URL with the appropriate query parameters
  const url = new URL(req.url, 'http://localhost');

  // If caller is a client, filter by clientId
  if (req.client?._id) {
    url.searchParams.set('clientId', req.client._id.toString());
  }
  // If caller is a user (manager/admin), filter by managerId
  else if (req.user?._id) {
    url.searchParams.set('managerId', req.user._id.toString());
  } else {
    return res.status(403).json({
      status: 'failed',
      message: 'Unauthorized access'
    });
  }

  const bookings = await advancedResults<BookingDoc, BookingDocumentResult & Document>(url.toString(), Booking);

  await Booking.populate(bookings.results, {
    path: 'car',
    populate: [
      { path: 'carImages', select: '-_id -isActive' },
      { path: 'brand', select: 'name -_id' },
      { path: 'category', select: 'name -_id' }
    ]
  });

  return res.status(200).json({
    status: 'success',
    data: bookings
  });
});

export const getOneBooking = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const booking = await Booking.findOne({ _id: id });

  if (!booking) {
    return res.status(404).json({
      status: 'success',
      message: `Booking not found with id ${id}`
    });
  }

  // Check if the booking belongs to the caller
  if (req.client && booking.clientId.toString() !== req.client._id.toString()) {
    return res.status(403).json({
      status: 'failed',
      message: 'You are not authorized to view this booking'
    });
  }

  if (req.user && booking.managerId.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      status: 'failed',
      message: 'You are not authorized to view this booking'
    });
  }

  await Booking.populate(booking, { path: 'clientId' });
  await Booking.populate(booking, { path: 'managerId' });
  await Booking.populate<BookingDocExt>(booking, {
    path: 'car',
    select: '-_id -isActive',
    populate: [
      { path: 'carImages', select: '-_id -isActive' },
      { path: 'brand', select: 'name -_id' },
      { path: 'category', select: 'name -_id' }
    ]
  });
  return res.status(200).json({
    status: 'success',
    data: booking
  });
});

export const cancelBooking = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const booking = await Booking.findByIdAndUpdate({ _id: id }, { status: BOOKING_STATUS.CANCELED }, { new: true });

  if (!booking) {
    return res.status(404).json({
      status: 'success',
      message: `Booking not found with id ${id}`
    });
  }

  await Booking.populate<BookingDocExt>(booking, {
    path: 'car',
    select: '-_id -isActive',
    populate: [
      { path: 'carImages', select: '-_id -isActive' },
      { path: 'brand', select: 'name -_id' },
      { path: 'category', select: 'name -_id' }
    ]
  });

  // Send self-cancellation email
  await sendCanceledBookingEmail([booking], false);

  return res.status(200).json({
    status: 'success',
    data: booking
  });
});

export const approveBooking = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { accept } = req.body as { accept: boolean };
  if (accept === undefined) {
    return res.status(422).json({
      status: 'failed',
      message: 'Accept field is required'
    });
  }
  const booking = await Booking.findOne({ _id: id });

  if (!booking) {
    return res.status(404).json({
      status: 'success',
      message: `Booking not found with id ${id}`
    });
  }

  if (booking.approvalStatus !== APPROVAL_STATUS.PENDING) {
    return res.status(422).json({
      status: 'failed',
      message: 'Booking has already been approved or declined'
    });
  }
  let updatedBooking: BookingDoc;

  if (accept === true) {
    updatedBooking = await Booking.findByIdAndUpdate(
      { _id: id },
      { approvalStatus: APPROVAL_STATUS.APPROVED },
      { new: true }
    );

    // Update car earnings and wallet when booking is approved
    const car = await Car.findById(booking.car);
    if (car) {
      try {
        // Calculate platform fee (20% of manager total)
        const grossAmount = booking.managerTotal;
        const platformFee = WalletService.calculatePlatformFee(grossAmount);
        const netAmount = grossAmount - platformFee;

        // Ensure wallet exists for the company
        await WalletService.ensureWalletExists(car.company, req.user._id);

        // Add earnings to wallet using the new service
        await WalletService.addEarnings(
          car.company,
          grossAmount,
          netAmount,
          platformFee,
          {
            amount: netAmount,
            description: `Earnings from booking ${booking._id.toString()}`,
            bookingId: booking._id,
            reference: `EARNING_${booking._id.toString()}`,
            metadata: {
              bookingId: booking._id.toString(),
              carId: car._id.toString(),
              originalManagerTotal: booking.managerTotal
            }
          },
          req.user._id
        );

        // Update car total earnings and booking count
        await Car.findByIdAndUpdate(
          { _id: car._id },
          {
            $inc: {
              totalEarned: grossAmount, // Track gross earnings on car
              managerTotalEarned: netAmount, // Track net earnings for manager
              bookingCount: 1
            }
          }
        );

        // Booking approved successfully
      } catch (error) {
        console.error('Error processing wallet earnings:', error);
        // Continue with booking approval even if wallet update fails
        // This ensures booking approval isn't blocked by wallet issues
      }
    }
  } else {
    updatedBooking = await Booking.findByIdAndUpdate(
      { _id: id },
      { approvalStatus: APPROVAL_STATUS.DECLINED },
      { new: true }
    );

    // Send notifications and emails to super admin users when booking is declined
    await sendDeclineNotifications(booking);
  }

  await Booking.populate<BookingDocExt>(updatedBooking, {
    path: 'car',
    select: '-_id -isActive',
    populate: [
      { path: 'carImages', select: '-_id -isActive' },
      { path: 'brand', select: 'name -_id' },
      { path: 'category', select: 'name -_id' }
    ]
  });

  return res.status(200).json({
    status: 'success',
    data: updatedBooking
  });
});

// Helper function to send decline notifications
const sendDeclineNotifications = async (booking: BookingDoc) => {
  try {
    // Get super admin users
    const superAdmins = await User.find({ role: USER_ROLE.SUPER_ADMIN, isActive: true });

    // Get client and car details
    const client = await Client.findById(booking.clientId);
    const car = await Car.findById(booking.car);

    if (!client || !car) {
      return;
    }

    const message = `Booking declined for ${car.model} by client ${client.phoneNumber}`;

    // Send push notifications to super admins
    for (const admin of superAdmins) {
      if (admin.fcmToken) {
        try {
          await sendNotification(
            admin.fcmToken,
            'Booking Declined by Manager',
            message,
            'BOOKING_DECLINED',
            admin._id.toString(),
            undefined
          );
        } catch (error) {
          console.error(`Failed to send push notification to admin ${admin._id.toString()}:`, error);
        }
      }
    }

    // Send email to super admins
    try {
      const { emailTemplates } = await import('../consts.js');
      const emailTemplate = emailTemplates.BOOKING_DECLINED_NOTIFICATION;

      await triggerEmail({
        doc: {
          ...booking,
          clientPhone: client.phoneNumber,
          carModel: car.model,
          createdAt: booking.createdAt,
          year: new Date().getFullYear()
        },
        roles: [USER_ROLE.SUPER_ADMIN],
        template: emailTemplate.template,
        subject: emailTemplate.subject
      });
    } catch (error) {
      console.error('Failed to send decline email to super admins:', error);
    }
  } catch (error) {
    console.error('Error in sendDeclineNotifications:', error);
  }
};

export const getDeclinedBookings = asyncHandler(async (_req: Request, res: Response) => {
  // Find bookings that are declined by managers but still have status BOOKED
  const declinedBookings = await Booking.find({
    approvalStatus: APPROVAL_STATUS.DECLINED,
    status: BOOKING_STATUS.BOOKED
  });

  await Booking.populate(declinedBookings, {
    path: 'car',
    populate: [
      { path: 'carImages', select: '-_id -isActive' },
      { path: 'brand', select: 'name -_id' },
      { path: 'category', select: 'name -_id' }
    ]
  });

  await Booking.populate(declinedBookings, {
    path: 'clientId',
    select: 'phoneNumber email firstName lastName'
  });

  return res.status(200).json({
    status: 'success',
    data: declinedBookings
  });
});

export const getManagerBooking = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;

  const pipeline = [
    {
      $match: { managerId: user._id }
    },
    {
      $lookup: {
        from: 'cars',
        localField: 'car',
        foreignField: '_id',
        as: 'car',
        pipeline: [
          {
            $lookup: {
              from: 'carimages',
              localField: '_id',
              foreignField: 'carId',
              as: 'carImages',
              pipeline: [{ $match: { isActive: true } }, { $project: { _id: 0, isActive: 0 } }]
            }
          },
          {
            $lookup: {
              from: 'brands',
              localField: 'brand',
              foreignField: '_id',
              as: 'brand'
            }
          },
          {
            $unwind: {
              path: '$brand',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $lookup: {
              from: 'carcategories',
              localField: 'category',
              foreignField: '_id',
              as: 'category'
            }
          },
          {
            $unwind: {
              path: '$category',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $project: {
              _id: 1,
              model: 1,
              year: 1,
              dailyPrice: 1,
              dailyMinPrice: 1,
              address: 1,
              schedule: 1,
              description: 1,
              bookingCount: 1,
              totalEarned: 1,
              carImages: 1,
              brand: { name: '$brand.name' },
              category: { name: '$category.name' }
            }
          }
        ]
      }
    },
    {
      $unwind: {
        path: '$car',
        preserveNullAndEmptyArrays: true
      }
    }
  ];

  const bookings = await Booking.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: bookings
  });
});

export const getClientBooking = asyncHandler(async (req: Request, res: Response) => {
  const client = req.client;

  const pipeline = [
    {
      $match: { clientId: client._id }
    },
    {
      $lookup: {
        from: 'cars',
        localField: 'car',
        foreignField: '_id',
        as: 'car',
        pipeline: [
          {
            $lookup: {
              from: 'carimages',
              localField: '_id',
              foreignField: 'carId',
              as: 'carImages',
              pipeline: [{ $match: { isActive: true } }, { $project: { _id: 0, isActive: 0 } }]
            }
          },
          {
            $lookup: {
              from: 'brands',
              localField: 'brand',
              foreignField: '_id',
              as: 'brand'
            }
          },
          {
            $unwind: {
              path: '$brand',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $lookup: {
              from: 'carcategories',
              localField: 'category',
              foreignField: '_id',
              as: 'category'
            }
          },
          {
            $unwind: {
              path: '$category',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $project: {
              _id: 1,
              model: 1,
              year: 1,
              dailyPrice: 1,
              dailyMinPrice: 1,
              address: 1,
              schedule: 1,
              description: 1,
              bookingCount: 1,
              totalEarned: 1,
              carImages: 1,
              brand: { name: '$brand.name' },
              category: { name: '$category.name' }
            }
          }
        ]
      }
    },
    {
      $unwind: {
        path: '$car',
        preserveNullAndEmptyArrays: true
      }
    }
  ];

  const bookings = await Booking.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: bookings
  });
});

export const previewBooking = asyncHandler(async (req: Request, res: Response) => {
  const { error } = createBookingApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const body = req.body as RegisterBookingRequestBody;
  const {
    carId,
    startDate,
    endDate,
    startTime,
    pickupAddress,
    destinationAddress,
    escortCount,
    escortDays,
    hasAdditionalStop
  } = body;

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (new Date(startDate) < today) {
    return res.status(422).json({
      status: 'failed',
      message: 'Start date cannot be before today'
    });
  }

  if (new Date(endDate) < new Date(startDate)) {
    return res.status(422).json({
      status: 'failed',
      message: 'End date cannot be before start date'
    });
  }

  const car = await Car.findOne({ _id: carId });

  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  // Create a temporary booking object for calculation
  const tempBooking: any = {
    car: carId,
    startDate,
    endDate,
    hasAdditionalStop,
    startTime,
    pickupAddress: addPointsToAddress(pickupAddress),
    destinationAddress: addPointsToAddress(destinationAddress),
    escortCount: escortCount || 0,
    escortDays: escortDays || 0,
    numberOfDays: 0, // Will be calculated below
    pickupKilometer: 0, // Will be calculated below
    destinationKilometer: 0 // Will be calculated below
  };

  // Calculate booking details
  const { calculateDaysBetween } = await import('../helpers/application_helper.js');
  const { calculateBookingCharges, calculateDistance, formatDistanceKm } = await import('../helpers/car_helper.js');

  const numberOfDays = calculateDaysBetween(tempBooking.startDate, tempBooking.endDate);
  tempBooking.numberOfDays = numberOfDays;

  const pickupKilometer = calculateDistance(car.address, tempBooking.pickupAddress);
  tempBooking.pickupKilometer = pickupKilometer;
  const destinationKilometer = calculateDistance(tempBooking.pickupAddress, tempBooking.destinationAddress);
  tempBooking.destinationKilometer = destinationKilometer;

  const bookingCharges = calculateBookingCharges(car, tempBooking as BookingDoc);
  const { total, managerTotal, escortsTotal, vat } = bookingCharges;

  return res.status(200).json({
    status: 'success',
    message: 'Booking preview calculated successfully',
    data: {
      numberOfDays,
      carToPickupDistanceKm: formatDistanceKm(pickupKilometer),
      pickupToDestinationDistanceKm: formatDistanceKm(destinationKilometer),
      totalDistanceKm: formatDistanceKm(pickupKilometer + destinationKilometer),
      totalPrice: total,
      managerTotal,
      escortsTotal,
      vat,
      carDetails: {
        model: car.model,
        dailyPrice: car.dailyPrice,
        dailyMinPrice: car.dailyMinPrice
      }
    }
  });
});

// get bookings meta
export const getBookingsMeta = asyncHandler(async (req: Request, res: Response) => {
  try {
    const totalBookings = await Booking.countDocuments();
    const totalBookingsPendingPayment = await Booking.countDocuments({ status: BOOKING_STATUS.PENDING });
    const totalBookingsPendingApproval = await Booking.countDocuments({
      status: BOOKING_STATUS.BOOKED,
      approvalStatus: APPROVAL_STATUS.PENDING
    });
    const totalBookingsPending = totalBookingsPendingPayment + totalBookingsPendingApproval;
    const totalBookingsPaid = await Booking.countDocuments({ status: BOOKING_STATUS.BOOKED });
    const totalBookingsCancelled = await Booking.countDocuments({ status: BOOKING_STATUS.CANCELED });
    const totalBookingsCompleted = await Booking.countDocuments({ status: BOOKING_STATUS.COMPLETED });
    const totalBookingsInProgress = await Booking.countDocuments({ status: BOOKING_STATUS.IN_PROGRESS });
    const totalBookingsApproved = await Booking.countDocuments({ approvalStatus: APPROVAL_STATUS.APPROVED });
    const totalBookingsDeclined = await Booking.countDocuments({ approvalStatus: APPROVAL_STATUS.DECLINED });

    return res.status(200).json({
      status: 'success',
      data: {
        totalBookings,
        totalBookingsPending,
        totalBookingsPendingPayment,
        totalBookingsPendingApproval,
        totalBookingsPaid,
        totalBookingsApproved,
        totalBookingsDeclined,
        totalBookingsCancelled,
        totalBookingsCompleted,
        totalBookingsInProgress
      }
    });
  } catch (error) {
    return res.status(500).json({
      status: 'failed',
      message: 'Internal server error'
    });
  }
});
