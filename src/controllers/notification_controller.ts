import { Request, Response } from 'express';

import { createNotificationApiValidator } from '../api_validators/notification-api-validator.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import firebase from '../helpers/firebase.js';
import Notification from '../models/NotificationModel/NotificationModel.js';
import { RegisterNotificationRequestBody } from '../types/notification.js';

export const createNotification = asyncHandler(async (req: Request, res: Response) => {
  const reqBody = req.body as RegisterNotificationRequestBody;
  const { userId, clientId, action, body, title, fcmToken } = reqBody;

  const { error } = createNotificationApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const message = {
    token: fcmToken,
    notification: {
      title,
      body
    },
    data: {
      action,
      title,
      body,
      userId: userId ? userId?.toString() : '',
      clientId: clientId ? clientId?.toString() : ''
    }
  };

  try {
    await firebase.messaging().send(message);
  } catch (error: unknown) {
    return res.status(422).json({
      status: 'error',
      message: 'Could not Create Notification',
      data: error
    });
  }

  const notification = new Notification({
    userId,
    clientId,
    action,
    body,
    title,
    fcmToken,
    readStatus: 'sent'
  });
  await notification.save();

  return res.status(201).json({
    status: 'success',
    message: 'Notification created Successfully',
    data: notification
  });
});

export const getClientNotification = asyncHandler(async (req: Request, res: Response) => {
  const clientId = req.client._id;
  const notifications = await Notification.find({ clientId });

  return res.status(200).json({
    status: 'success',
    data: notifications
  });
});

export const getManagerNotification = asyncHandler(async (req: Request, res: Response) => {
  const { managerId } = req.params as { managerId?: string };
  const notifications = await Notification.find({ managerId });

  return res.status(200).json({
    status: 'success',
    data: notifications
  });
});

export const updateNotifications = asyncHandler(async (req: Request, res: Response) => {
  const { notificationId } = req.params;

  const updateNotifications = await Notification.findByIdAndUpdate(
    {
      _id: notificationId
    },
    { isRead: true },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    data: updateNotifications
  });
});

export const deleteNotification = asyncHandler(async (req: Request, res: Response) => {
  const { notificationId } = req.params;

  const notification = await Notification.findOne({ _id: notificationId });

  if (!notification) {
    return res.status(404).json({
      status: 'failed',
      message: 'Notification not found'
    });
  }

  await Notification.deleteOne({
    _id: notificationId
  });

  return res.status(200).json({
    status: 'success'
  });
});
