import mongoose from 'mongoose';

import Logger from '../libs/logger.js';
import Car from '../models/CarModel/CarModel.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import User from '../models/UserModel/UserModel.js';
import Wallet from '../models/WalletModel/WalletModel.js';
import { WalletService } from '../services/wallet_service.js';

/**
 * Migration script to update the wallet system
 * This script will:
 * 1. Create wallets for companies that don't have them
 * 2. Migrate existing transaction data to the new wallet structure
 * 3. Reconcile wallet balances with transaction history
 */

async function migrateWalletSystem() {
  try {
    Logger.info('Starting wallet system migration...');

    // Connect to MongoDB if not already connected
    if ((mongoose.connection.readyState as number) !== 1) {
      const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/luxlet';
      await mongoose.connect(mongoUri);
      Logger.info('Connected to MongoDB');
    }

    // Step 1: Create wallets for companies that don't have them
    await createMissingWallets();

    // Step 2: Reconcile all existing wallets
    await reconcileAllWallets();

    // Step 3: Validate wallet consistency
    await validateAllWallets();

    Logger.info('Wallet system migration completed successfully');
  } catch (error) {
    Logger.error('Error during wallet system migration:', error);
    throw error;
  }
}

async function createMissingWallets() {
  Logger.info('Creating missing wallets...');

  try {
    // Get all companies
    const companies = await Company.find({ isActive: true });
    Logger.info(`Found ${companies.length} active companies`);

    let walletsCreated = 0;

    for (const company of companies) {
      try {
        // Check if wallet already exists
        const existingWallet = await Wallet.findOne({ company: company._id, isActive: true });

        if (!existingWallet) {
          // Find a user from this company to use as creator
          const companyUser = await User.findOne({ company: company._id, isActive: true });

          if (companyUser) {
            await WalletService.createWallet({
              company: company._id,
              createdBy: companyUser._id
            });
            walletsCreated++;
            Logger.info(`Created wallet for company: ${company._id.toString()}`);
          } else {
            Logger.warn(`No active user found for company: ${company._id.toString()}, skipping wallet creation`);
          }
        }
      } catch (error) {
        Logger.error(`Error creating wallet for company ${company._id.toString()}:`, error);
        // Continue with other companies
      }
    }

    Logger.info(`Created ${walletsCreated} new wallets`);
  } catch (error) {
    Logger.error('Error in createMissingWallets:', error);
    throw error;
  }
}

async function reconcileAllWallets() {
  Logger.info('Reconciling all wallets...');

  try {
    // Get all active wallets
    const wallets = await Wallet.find({ isActive: true });
    Logger.info(`Found ${wallets.length} active wallets to reconcile`);

    let reconciledCount = 0;

    for (const wallet of wallets) {
      try {
        await WalletService.reconcileWallet(wallet.company);
        reconciledCount++;
        Logger.info(`Reconciled wallet for company: ${wallet.company.toString()}`);
      } catch (error) {
        Logger.error(`Error reconciling wallet for company ${wallet.company.toString()}:`, error);
        // Continue with other wallets
      }
    }

    Logger.info(`Reconciled ${reconciledCount} wallets`);
  } catch (error) {
    Logger.error('Error in reconcileAllWallets:', error);
    throw error;
  }
}

async function validateAllWallets() {
  Logger.info('Validating all wallets...');

  try {
    const wallets = await Wallet.find({ isActive: true });
    Logger.info(`Validating ${wallets.length} wallets`);

    let validWallets = 0;
    let inconsistentWallets = 0;

    for (const wallet of wallets) {
      try {
        const validation = await WalletService.validateWalletConsistency(wallet.company);

        if (validation.isConsistent) {
          validWallets++;
        } else {
          inconsistentWallets++;
          Logger.warn(`Wallet inconsistency found for company ${wallet.company.toString()}:`, validation.discrepancies);
        }
      } catch (error) {
        Logger.error(`Error validating wallet for company ${wallet.company.toString()}:`, error);
        inconsistentWallets++;
      }
    }

    Logger.info(`Validation complete: ${validWallets} consistent, ${inconsistentWallets} inconsistent wallets`);
  } catch (error) {
    Logger.error('Error in validateAllWallets:', error);
    throw error;
  }
}

/**
 * Initialize car manager total earned field
 */
async function initializeCarManagerTotalEarned() {
  Logger.info('Initializing car managerTotalEarned field...');

  try {
    // Add managerTotalEarned field to all cars that don't have it
    const result = await Car.updateMany(
      { managerTotalEarned: { $exists: false } },
      { $set: { managerTotalEarned: 0 } }
    );

    Logger.info(`Updated ${result.modifiedCount} cars with managerTotalEarned field`);
  } catch (error) {
    Logger.error('Error initializing car managerTotalEarned:', error);
    throw error;
  }
}

/**
 * Generate a summary report of the wallet system
 */
async function generateWalletReport() {
  Logger.info('Generating wallet system report...');

  try {
    const totalWallets = await Wallet.countDocuments({ isActive: true });
    const totalCompanies = await Company.countDocuments({ isActive: true });

    const walletStats = await Wallet.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: null,
          totalGrossEarnings: { $sum: '$grossEarnings' },
          totalNetEarnings: { $sum: '$netEarnings' },
          totalPlatformFees: { $sum: '$totalPlatformFees' },
          totalWithdrawn: { $sum: '$totalWithdrawn' },
          totalAvailableBalance: { $sum: '$availableBalance' }
        }
      }
    ]);

    const stats = walletStats[0] || {
      totalGrossEarnings: 0,
      totalNetEarnings: 0,
      totalPlatformFees: 0,
      totalWithdrawn: 0,
      totalAvailableBalance: 0
    };

    Logger.info('=== WALLET SYSTEM REPORT ===');
    Logger.info(`Total Companies: ${totalCompanies}`);
    Logger.info(`Total Wallets: ${totalWallets}`);
    Logger.info(`Wallet Coverage: ${((totalWallets / totalCompanies) * 100).toFixed(2)}%`);
    Logger.info(`Total Gross Earnings: ₦${stats.totalGrossEarnings.toLocaleString()}`);
    Logger.info(`Total Net Earnings: ₦${stats.totalNetEarnings.toLocaleString()}`);
    Logger.info(`Total Platform Fees: ₦${stats.totalPlatformFees.toLocaleString()}`);
    Logger.info(`Total Withdrawn: ₦${stats.totalWithdrawn.toLocaleString()}`);
    Logger.info(`Total Available Balance: ₦${stats.totalAvailableBalance.toLocaleString()}`);
    Logger.info('=== END REPORT ===');
  } catch (error) {
    Logger.error('Error generating wallet report:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await migrateWalletSystem();
    await initializeCarManagerTotalEarned();
    await generateWalletReport();

    Logger.info('All migration tasks completed successfully');
    process.exit(0);
  } catch (error) {
    Logger.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  void main();
}

export {
  createMissingWallets,
  initializeCarManagerTotalEarned,
  migrateWalletSystem,
  reconcileAllWallets,
  validateAllWallets
};
