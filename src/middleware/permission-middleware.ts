import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';

import Client from '../models/ClientModel/ClientModel.js';
import User from '../models/UserModel/UserModel.js';
import { ClientDoc } from '../types/client.js';
import { USER_ROLE, UserDoc } from '../types/user.js';

export const authorize = async (req: Request, res: Response, next: NextFunction, contAuth = false) => {
  const token = req.headers.authorization?.split(' ')[1];
  const JWT_SECRET = process.env.JWT_SECRET;

  if (!token) {
    return res.status(401).json({
      status: 'failed',
      error: 'Not authorized to access this route'
    });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { email: string } | string;
    if (typeof decoded === 'string') {
      throw new Error('Invalid token');
    }

    const user = await User.findOne({ email: decoded.email }).lean();

    if (!user) {
      if (contAuth) {
        return next();
      } else {
        return res.status(401).json({
          status: 'failed',
          error: 'User not authorized to access this route'
        });
      }
    }

    req.user = user as UserDoc;

    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        status: 'failed',
        error: 'Token has expired'
      });
    } else if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        status: 'failed',
        error: 'Invalid token'
      });
    } else {
      next(new Error('Something went wrong!'));
    }
  }
};

export const restrictToRoles = (permittedRoles: string[]) => (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = req.user as UserDoc | undefined;
    const client = req.client;
    const role = user?.role || (client ? 'client' : undefined);

    if (!role) {
      return res.status(401).json({
        status: 'failed',
        error: 'No permissions to access this route'
      });
    }

    if (role === USER_ROLE.SUPER_ADMIN || permittedRoles.includes(role)) {
      return next();
    } else {
      return res.status(403).json({
        status: 'failed',
        error: 'No permissions to access this route'
      });
    }
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        status: 'failed',
        error: 'Token has expired'
      });
    } else if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        status: 'failed',
        error: 'Invalid token'
      });
    } else {
      next(new Error('Something went wrong!'));
    }
  }
};

export const authorizeClient = async (req: Request, res: Response, next: NextFunction, contAuth = false) => {
  const token = req.headers.authorization?.split(' ')[1];
  const JWT_SECRET = process.env.JWT_SECRET;

  if (!token) {
    return res.status(401).json({
      status: 'failed',
      error: 'Not authorized to access this route'
    });
  }

  if (!JWT_SECRET) {
    return res.status(500).json({
      status: 'failed',
      error: 'Internal server error'
    });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { phoneNumber: string } | string;
    if (typeof decoded === 'string') {
      return res.status(401).json({
        status: 'failed',
        error: 'Invalid token format'
      });
    }

    if (!decoded.phoneNumber) {
      return res.status(401).json({
        status: 'failed',
        error: 'Invalid token payload'
      });
    }

    const client = await Client.findOne({ phoneNumber: decoded.phoneNumber });

    if (!client) {
      if (contAuth) {
        return next();
      } else {
        return res.status(401).json({
          status: 'failed',
          error: 'Client not authorized to access this route'
        });
      }
    }

    // Check if client is active
    if (!client.isActive) {
      return res.status(401).json({
        status: 'failed',
        error: 'Client account is inactive'
      });
    }

    req.client = client as ClientDoc;
    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        status: 'failed',
        error: 'Token has expired'
      });
    } else if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        status: 'failed',
        error: 'Invalid token'
      });
    } else {
      return res.status(500).json({
        status: 'failed',
        error: 'Internal server error'
      });
    }
  }
};

// export const authenticate = async (req: Request, res: Response, next: NextFunction, contAuth = false) => {
//   const token = req.headers.authorization?.split(' ')[1];
//   const JWT_SECRET = process.env.JWT_SECRET;

//   if (!token) {
//     return res.status(401).json({
//       status: 'failed',
//       error: 'Not authorized to access this route'
//     });
//   }

//   try {
//     const decoded = jwt.verify(token, JWT_SECRET) as { email?: string; phoneNumber?: string } | string;
//     if (typeof decoded === 'string') {
//       throw new Error('Invalid token');
//     }

//     // Try to find user first
//     if (decoded.email) {
//       const user = await User.findOne({ email: decoded.email });
//       if (user) {
//         req.user = user as UserDoc;
//         return next();
//       }
//     }

//     // If no user found, try to find client
//     if (decoded.phoneNumber) {
//       const client = await Client.findOne({ phoneNumber: decoded.phoneNumber });
//       if (client && client.phoneNumber === decoded.phoneNumber) {
//         req.client = client as ClientDoc;
//         return next();
//       }
//     }

//     // No user or client found
//     if (contAuth) {
//       return next();
//     }

//     return res.status(401).json({
//       status: 'failed',
//       error: 'Not authorized to access this route'
//     });
//   } catch (error) {
//     if (error instanceof jwt.TokenExpiredError) {
//       next(new Error('TokenExpiredError: jwt expired'));
//     } else if (error instanceof jwt.JsonWebTokenError) {
//       next(new Error('JsonWebTokenError: invalid token'));
//     } else {
//       next(new Error('Something went wrong!'));
//     }
//   }
// };
