/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { NextFunction, Request, Response } from 'express';
import mongoose from 'mongoose';

import {
  DB_DUPLICATE_VALUE_ERROR,
  DB_INVALID_IDDENTIFIER,
  DB_NETWORK_ERROR,
  DB_SERVER_ERROR,
  DB_VALIDATION_ERROR
} from '../libs/error/consts.js';

export const mongoErrorHandler = (err, req: Request, res: Response, next: NextFunction) => {
  if (err instanceof mongoose.Error.CastError) {
    return res.status(400).json({
      error: {
        message: DB_INVALID_IDDENTIFIER,
        details: { path: err.path, kind: err.kind, model: err?.reason.message }
      }
    });
  } else if (err instanceof mongoose.Error.ValidationError) {
    // Mongoose validation error
    return res.status(400).json({
      error: {
        message: DB_VALIDATION_ERROR,
        reason: err.message,
        details: err.errors // Optionally include validation error details
      }
    });
  } else if (err.code === 11000) {
    // Duplicate key error
    return res.status(409).json({
      error: {
        message: DB_DUPLICATE_VALUE_ERROR
      }
    });
  } else if (err.name === 'MongoNetworkError') {
    return res.status(503).json({
      error: {
        message: DB_NETWORK_ERROR
      }
    });
  } else if (err.name === 'MongoServerError') {
    return res.status(500).json({
      error: {
        message: DB_SERVER_ERROR
      }
    });
  } else {
    next(err);
  }
};
