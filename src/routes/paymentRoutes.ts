import express from 'express';

import { createPaymentInfo, getPaymentInfo } from '../controllers/payments_info_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create
router.post('/', authorize, restrictToRoles(['admin', 'manager']), createPaymentInfo);

// Fetch
router.get('/', authorize, restrictToRoles(['admin', 'manager']), getPaymentInfo);

export default router;
