import express from 'express';

import {
  createAdvert,
  deleteAdvert,
  getAdvertById,
  getAdverts,
  updateAdvert
} from '../controllers/advert_controller.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// More specific first
router.get('/client', authorizeClient, getAdverts);
router.post('/create', authorize, restrictToRoles(['admin']), createAdvert);

// Then general routes
router.get('/', authorize, restrictToRoles(['admin']), getAdverts);

// Routes with dynamic params (least specific — placed last)
router.get('/:id', authorize, restrictToRoles(['admin']), getAdvertById);
router.patch('/:id', authorize, restrictToRoles(['admin']), updateAdvert);
router.delete('/:id', authorize, restrictToRoles(['admin']), deleteAdvert);

export default router;
