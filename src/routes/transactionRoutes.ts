import express from 'express';

import {
  chargeMandate,
  createTransaction,
  getAllTransactions,
  getOneTransaction,
  removeMandate,
  verifyTransaction
} from '../controllers/transaction_controller.js';
import { authorizeClient } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create and process transactions
router.post('/create', authorizeClient, createTransaction);
router.post('/process', authorizeClient, verifyTransaction);

// Mandate actions
router.post('/charge-mandate', authorizeClient, chargeMandate);
router.delete('/delete-mandate', authorizeClient, removeMandate);

// Fetch transactions
router.get('/:id', authorizeClient, getOneTransaction);
router.get('/', authorizeClient, getAllTransactions);

export default router;
