import express from 'express';

import {
  deleteClient,
  getClient,
  getClients,
  sendClientOTP,
  updateClient,
  verifyClientOTP
} from '../controllers/clients_controller.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// OTP routes
router.post('/send-otp', sendClientOTP);
router.post('/verify-otp', verifyClientOTP);

// Client operations
router.get('/:id', authorizeClient, getClient);
router.put('/', authorizeClient, updateClient);
router.get('/', authorize, restrictToRoles(['admin']), getClients);
router.delete('/:id', authorize, restrictToRoles(['admin']), deleteClient);

export default router;
