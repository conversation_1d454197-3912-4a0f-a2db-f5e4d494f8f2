import express from 'express';

import {
  getWalletBalance,
  getWalletTransactions,
  reconcileWallet,
  validateWalletConsistency
} from '../controllers/wallet_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Wallet:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Wallet unique identifier
 *         company:
 *           type: string
 *           description: Company ID
 *         grossTotal:
 *           type: number
 *           description: Total gross earnings
 *           example: 150000
 *         netTotal:
 *           type: number
 *           description: Total net earnings after platform fees
 *           example: 135000
 *         platformFeeTotal:
 *           type: number
 *           description: Total platform fees collected
 *           example: 15000
 *         currentBalance:
 *           type: number
 *           description: Current available balance
 *           example: 120000
 *         totalWithdrawn:
 *           type: number
 *           description: Total amount withdrawn
 *           example: 15000
 *         totalRefunded:
 *           type: number
 *           description: Total amount refunded
 *           example: 0
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     WalletTransaction:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         wallet:
 *           type: string
 *           description: Wallet ID
 *         type:
 *           type: string
 *           enum: [EARNING, WITHDRAWAL, REFUND]
 *           description: Transaction type
 *         amount:
 *           type: number
 *           description: Transaction amount
 *         grossAmount:
 *           type: number
 *           description: Gross amount (for earnings)
 *         platformFee:
 *           type: number
 *           description: Platform fee (for earnings)
 *         booking:
 *           type: string
 *           description: Related booking ID (for earnings/refunds)
 *         description:
 *           type: string
 *           description: Transaction description
 *         balanceAfter:
 *           type: number
 *           description: Wallet balance after transaction
 *         createdAt:
 *           type: string
 *           format: date-time
 *
 *     WalletValidation:
 *       type: object
 *       properties:
 *         isValid:
 *           type: boolean
 *           description: Whether wallet data is consistent
 *         discrepancies:
 *           type: array
 *           items:
 *             type: string
 *           description: List of found discrepancies
 *         calculatedBalance:
 *           type: number
 *           description: Calculated balance from transactions
 *         actualBalance:
 *           type: number
 *           description: Current wallet balance
 */

/**
 * @swagger
 * /wallets:
 *   get:
 *     summary: Get wallet balance
 *     description: Retrieve wallet balance and summary for the authenticated manager's company
 *     tags: [Wallet Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Wallet balance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Wallet'
 *       401:
 *         description: Unauthorized - Admin/Manager access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Wallet not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', authorize, restrictToRoles(['admin', 'manager']), getWalletBalance);

/**
 * @swagger
 * /wallets/transactions:
 *   get:
 *     summary: Get wallet transaction history
 *     description: Retrieve transaction history for the authenticated manager's wallet
 *     tags: [Wallet Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [EARNING, WITHDRAWAL, REFUND]
 *         description: Filter by transaction type
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter transactions from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter transactions until this date
 *     responses:
 *       200:
 *         description: Wallet transactions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/WalletTransaction'
 *       401:
 *         description: Unauthorized - Admin/Manager access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/transactions', authorize, restrictToRoles(['admin', 'manager']), getWalletTransactions);

/**
 * @swagger
 * /wallets/reconcile:
 *   post:
 *     summary: Reconcile wallet balances
 *     description: Reconcile wallet balances by recalculating from transaction history
 *     tags: [Wallet Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Wallet reconciled successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         previousBalance:
 *                           type: number
 *                         newBalance:
 *                           type: number
 *                         adjustmentMade:
 *                           type: boolean
 *       401:
 *         description: Unauthorized - Admin/Manager access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Wallet not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/reconcile', authorize, restrictToRoles(['admin', 'manager']), reconcileWallet);

/**
 * @swagger
 * /wallets/validate:
 *   get:
 *     summary: Validate wallet consistency
 *     description: Validate wallet data consistency and identify any discrepancies
 *     tags: [Wallet Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Wallet validation completed
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/WalletValidation'
 *       401:
 *         description: Unauthorized - Admin/Manager access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Wallet not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/validate', authorize, restrictToRoles(['admin', 'manager']), validateWalletConsistency);

export default router;
