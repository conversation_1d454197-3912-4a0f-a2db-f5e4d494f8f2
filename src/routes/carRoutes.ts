import express from 'express';

import {
  createCar,
  createCategory,
  deleteCarCategory,
  deleteCarImages,
  delistCar,
  fetchCarsForClient,
  getBrandsAndCategories,
  getCar,
  getCars,
  updateCar,
  updateCarCategory,
  updateImageVisibility,
  updateMainImage,
  uploadCarImages
} from '../controllers/car_controller.js';
import { handleMulterError, upload } from '../helpers/file_upload.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Car:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Car unique identifier
 *         model:
 *           type: string
 *           description: Car model name
 *           example: "Toyota Camry"
 *         year:
 *           type: number
 *           description: Manufacturing year
 *           example: 2022
 *         dailyPrice:
 *           type: number
 *           description: Daily rental price
 *           example: 15000
 *         dailyMinPrice:
 *           type: number
 *           description: Minimum daily price
 *           example: 12000
 *         description:
 *           type: string
 *           description: Car description
 *         brand:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *               example: "Toyota"
 *         category:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *               example: "Sedan"
 *         address:
 *           type: object
 *           properties:
 *             street:
 *               type: string
 *             city:
 *               type: string
 *             state:
 *               type: string
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *         schedule:
 *           type: string
 *           enum: [DAY, NIGHT]
 *           description: Car availability schedule
 *         carImages:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               thumbnailUrl:
 *                 type: string
 *               mediumUrl:
 *                 type: string
 *               bigUrl:
 *                 type: string
 *               isMainImage:
 *                 type: boolean
 *         bookingCount:
 *           type: number
 *           description: Total number of bookings
 *         totalEarned:
 *           type: number
 *           description: Total earnings from this car
 *         managerTotalEarned:
 *           type: number
 *           description: Manager's total earnings from this car
 *         carToPickupDistanceKm:
 *           type: string
 *           description: Distance from car to pickup location
 *         pickupToDestinationDistanceKm:
 *           type: string
 *           description: Distance from pickup to destination
 *         totalDistanceKm:
 *           type: string
 *           description: Total distance for the trip
 *
 *     CreateCarRequest:
 *       type: object
 *       required:
 *         - model
 *         - year
 *         - dailyPrice
 *         - dailyMinPrice
 *         - brand
 *         - category
 *         - address
 *         - schedule
 *       properties:
 *         model:
 *           type: string
 *           example: "Toyota Camry"
 *         year:
 *           type: number
 *           example: 2022
 *         dailyPrice:
 *           type: number
 *           example: 15000
 *         dailyMinPrice:
 *           type: number
 *           example: 12000
 *         description:
 *           type: string
 *           example: "Comfortable sedan with AC"
 *         brand:
 *           type: string
 *           description: Brand ID
 *           example: "60d5ecb74b24a1234567890a"
 *         category:
 *           type: string
 *           description: Category ID
 *           example: "60d5ecb74b24a1234567890b"
 *         address:
 *           type: object
 *           required:
 *             - street
 *             - city
 *             - state
 *             - coordinates
 *           properties:
 *             street:
 *               type: string
 *               example: "123 Main Street"
 *             city:
 *               type: string
 *               example: "Lagos"
 *             state:
 *               type: string
 *               example: "Lagos"
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *               example: [6.5244, 3.3792]
 *         schedule:
 *           type: string
 *           enum: [DAY, NIGHT]
 *           example: "DAY"
 *
 *     Category:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         name:
 *           type: string
 *           example: "Sedan"
 *         thumbnailUrl:
 *           type: string
 *         mediumUrl:
 *           type: string
 *         bigUrl:
 *           type: string
 *
 *     CreateCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           example: "SUV"
 *
 *     BrandsAndCategories:
 *       type: object
 *       properties:
 *         brands:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               _id:
 *                 type: string
 *               name:
 *                 type: string
 *         categories:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Category'
 */

/**
 * @swagger
 * /cars/category:
 *   post:
 *     summary: Create a new car category
 *     description: Create a new category for cars with image upload
 *     tags: [Car Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - image
 *             properties:
 *               name:
 *                 type: string
 *                 example: "SUV"
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Category image file
 *     responses:
 *       201:
 *         description: Category created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Category'
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - Admin access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  '/category',
  authorize,
  restrictToRoles(['admin']),
  upload.single('image'),
  handleMulterError,
  createCategory
);

/**
 * @swagger
 * /cars/category/{id}:
 *   put:
 *     summary: Update car category
 *     description: Update an existing car category
 *     tags: [Car Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Category ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCategoryRequest'
 *     responses:
 *       200:
 *         description: Category updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete car category
 *     description: Delete an existing car category
 *     tags: [Car Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Category ID
 *     responses:
 *       200:
 *         description: Category deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/category/:id', authorize, restrictToRoles(['admin']), updateCarCategory);
router.delete('/category/:id', authorize, restrictToRoles(['admin']), deleteCarCategory);

/**
 * @swagger
 * /cars/brands-and-categories:
 *   get:
 *     summary: Get all brands and categories
 *     description: Retrieve all available car brands and categories
 *     tags: [Car Data]
 *     responses:
 *       200:
 *         description: Brands and categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BrandsAndCategories'
 */
router.get('/brands-and-categories', getBrandsAndCategories);

/**
 * @swagger
 * /cars/categories/get:
 *   get:
 *     summary: Get available cars for client
 *     description: Fetch cars available for client booking with filtering options
 *     tags: [Client Cars]
 *     parameters:
 *       - in: query
 *         name: pickupCoordinates
 *         schema:
 *           type: string
 *         description: Pickup coordinates as "lat,lng"
 *         example: "6.5244,3.3792"
 *       - in: query
 *         name: destinationCoordinates
 *         schema:
 *           type: string
 *         description: Destination coordinates as "lat,lng"
 *         example: "6.4474,3.3903"
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Booking start date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Booking end date
 *       - in: query
 *         name: schedule
 *         schema:
 *           type: string
 *           enum: [DAY, NIGHT]
 *         description: Car schedule preference
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Category ID filter
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Brand ID filter
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *         description: State filter
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum daily price
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum daily price
 *     responses:
 *       200:
 *         description: Available cars retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Car'
 */
router.get('/categories/get', fetchCarsForClient);

/**
 * @swagger
 * /cars/images:
 *   post:
 *     summary: Upload car images
 *     description: Upload multiple images for a car
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - carId
 *               - images
 *             properties:
 *               carId:
 *                 type: string
 *                 description: Car ID
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Image files to upload
 *     responses:
 *       200:
 *         description: Images uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid input or file format
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   put:
 *     summary: Update image visibility
 *     description: Update visibility status of car images
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imageId
 *               - isVisible
 *             properties:
 *               imageId:
 *                 type: string
 *                 description: Image ID
 *               isVisible:
 *                 type: boolean
 *                 description: Visibility status
 *     responses:
 *       200:
 *         description: Image visibility updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 */
router.post(
  '/images',
  authorize,
  restrictToRoles(['admin', 'manager']),
  upload.array('images'),
  handleMulterError,
  uploadCarImages
);
router.put('/images', authorize, restrictToRoles(['manager', 'admin']), updateImageVisibility);

/**
 * @swagger
 * /cars/images/{id}:
 *   patch:
 *     summary: Update main image
 *     description: Set an image as the main image for a car
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Image ID
 *     responses:
 *       200:
 *         description: Main image updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Image not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete car image
 *     description: Delete a specific car image
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Image ID
 *     responses:
 *       200:
 *         description: Image deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Image not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch('/images/:id', authorize, restrictToRoles(['admin', 'manager']), updateMainImage);
router.delete('/images/:id', authorize, restrictToRoles(['admin', 'manager']), deleteCarImages);

/**
 * @swagger
 * /cars:
 *   post:
 *     summary: Create a new car
 *     description: Create a new car for rental
 *     tags: [Car Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCarRequest'
 *     responses:
 *       201:
 *         description: Car created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Car'
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   get:
 *     summary: Get all cars
 *     description: Retrieve all cars for admin/manager view
 *     tags: [Car Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for car model
 *     responses:
 *       200:
 *         description: Cars retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Car'
 */
router.post('/', authorize, restrictToRoles(['admin', 'manager']), createCar);
router.get('/', authorize, restrictToRoles(['manager', 'admin']), getCars);

/**
 * @swagger
 * /cars/{id}:
 *   get:
 *     summary: Get car by ID
 *     description: Retrieve a specific car by ID (admin/manager view)
 *     tags: [Car Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Car ID
 *     responses:
 *       200:
 *         description: Car retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Car'
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   patch:
 *     summary: Update car
 *     description: Update an existing car
 *     tags: [Car Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Car ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCarRequest'
 *     responses:
 *       200:
 *         description: Car updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete (delist) car
 *     description: Soft delete a car by marking it as inactive
 *     tags: [Car Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Car ID
 *     responses:
 *       200:
 *         description: Car delisted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', authorize, restrictToRoles(['manager', 'admin']), getCar);
router.patch('/:id', authorize, restrictToRoles(['manager', 'admin']), updateCar);
router.delete('/:id', authorize, restrictToRoles(['admin', 'manager']), delistCar);

/**
 * @swagger
 * /cars/{id}/client:
 *   get:
 *     summary: Get car for client
 *     description: Retrieve a specific car for client view with distance calculations
 *     tags: [Client Cars]
 *     security:
 *       - clientAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Car ID
 *       - in: query
 *         name: pickupCoordinates
 *         schema:
 *           type: string
 *         description: Pickup coordinates as "lat,lng"
 *         example: "6.5244,3.3792"
 *       - in: query
 *         name: destinationCoordinates
 *         schema:
 *           type: string
 *         description: Destination coordinates as "lat,lng"
 *         example: "6.4474,3.3903"
 *     responses:
 *       200:
 *         description: Car retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Car'
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/client', authorizeClient, getCar);

export default router;
