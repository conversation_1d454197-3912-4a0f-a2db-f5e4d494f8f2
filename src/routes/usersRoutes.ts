import express from 'express';

import {
  deleteUser,
  getAdmins,
  getAUser,
  getLoggedInUser,
  getManagers,
  getSuperAdmins,
  getUserCars,
  getUsers,
  updateUser,
  updateUserRole,
  verifyOTP
} from '../controllers/users_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// General user routes
router.get('/', authorize, restrictToRoles(['admin']), getUsers);
router.get('/me', authorize, restrictToRoles(['manager', 'admin']), getLoggedInUser);
router.delete('/me', authorize, restrictToRoles(['manager', 'admin']), deleteUser);
router.put('/me', authorize, restrictToRoles(['manager', 'admin']), updateUser);

// Role-specific user lists
router.get('/admins', authorize, restrictToRoles(['super-admin']), getAdmins);
router.get('/super-admins', authorize, restrictToRoles(['super-admin']), getSuperAdmins);
router.get('/managers', authorize, restrictToRoles(['admin']), getManagers);

// Individual user routes
router.get('/:id', authorize, restrictToRoles(['admin']), getAUser);
router.patch('/:id', authorize, restrictToRoles(['super-admin']), updateUserRole);

// User sub-resources
router.get('/:id/cars', authorize, restrictToRoles(['admin']), getUserCars);
router.put('/:id/verify/:code', authorize, restrictToRoles(['admin', 'user']), verifyOTP);

export default router;
