import express from 'express';

import { createManager, getManager, updateManager } from '../controllers/manager_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create
router.post('/create', authorize, restrictToRoles(['manager']), createManager);

// Fetch
router.get('/:id', authorize, restrictToRoles(['admin']), getManager);

// Update
router.put('/:id', authorize, restrictToRoles(['admin', 'user']), updateManager);

export default router;
