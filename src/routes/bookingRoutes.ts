import express from 'express';

import {
  approveBooking,
  cancelBooking,
  createBooking,
  getBookingsMeta,
  getClientBooking,
  getDeclinedBookings,
  getManagerBooking,
  getOneBooking,
  getRequesterBookings,
  previewBooking
} from '../controllers/bookings_controller.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Booking:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Booking unique identifier
 *         car:
 *           type: object
 *           properties:
 *             _id:
 *               type: string
 *             model:
 *               type: string
 *             year:
 *               type: number
 *             dailyPrice:
 *               type: number
 *         client:
 *           type: object
 *           properties:
 *             _id:
 *               type: string
 *             firstName:
 *               type: string
 *             lastName:
 *               type: string
 *             phoneNumber:
 *               type: string
 *         startDate:
 *           type: string
 *           format: date
 *           description: Booking start date
 *         endDate:
 *           type: string
 *           format: date
 *           description: Booking end date
 *         pickupAddress:
 *           type: object
 *           properties:
 *             street:
 *               type: string
 *             city:
 *               type: string
 *             state:
 *               type: string
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *         destinationAddress:
 *           type: object
 *           properties:
 *             street:
 *               type: string
 *             city:
 *               type: string
 *             state:
 *               type: string
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *         totalAmount:
 *           type: number
 *           description: Total booking amount
 *         platformFee:
 *           type: number
 *           description: Platform fee
 *         netAmount:
 *           type: number
 *           description: Net amount after platform fee
 *         status:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED, CANCELLED, COMPLETED]
 *           description: Booking status
 *         schedule:
 *           type: string
 *           enum: [DAY, NIGHT]
 *           description: Booking schedule
 *         escortService:
 *           type: boolean
 *           description: Whether escort service is requested
 *         escortFee:
 *           type: number
 *           description: Escort service fee
 *         carToPickupDistanceKm:
 *           type: string
 *           description: Distance from car to pickup location
 *         pickupToDestinationDistanceKm:
 *           type: string
 *           description: Distance from pickup to destination
 *         totalDistanceKm:
 *           type: string
 *           description: Total distance for the trip
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     CreateBookingRequest:
 *       type: object
 *       required:
 *         - carId
 *         - startDate
 *         - endDate
 *         - pickupAddress
 *         - destinationAddress
 *         - schedule
 *       properties:
 *         carId:
 *           type: string
 *           description: Car ID to book
 *           example: "60d5ecb74b24a1234567890a"
 *         startDate:
 *           type: string
 *           format: date
 *           description: Booking start date
 *           example: "2024-01-15"
 *         endDate:
 *           type: string
 *           format: date
 *           description: Booking end date
 *           example: "2024-01-17"
 *         pickupAddress:
 *           type: object
 *           required:
 *             - street
 *             - city
 *             - state
 *             - coordinates
 *           properties:
 *             street:
 *               type: string
 *               example: "123 Victoria Island"
 *             city:
 *               type: string
 *               example: "Lagos"
 *             state:
 *               type: string
 *               example: "Lagos"
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *               example: [6.4281, 3.4219]
 *         destinationAddress:
 *           type: object
 *           required:
 *             - street
 *             - city
 *             - state
 *             - coordinates
 *           properties:
 *             street:
 *               type: string
 *               example: "456 Ikeja GRA"
 *             city:
 *               type: string
 *               example: "Lagos"
 *             state:
 *               type: string
 *               example: "Lagos"
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *               example: [6.5955, 3.3087]
 *         schedule:
 *           type: string
 *           enum: [DAY, NIGHT]
 *           description: Booking schedule
 *           example: "DAY"
 *         escortService:
 *           type: boolean
 *           description: Whether to include escort service
 *           example: false
 *
 *     BookingPreview:
 *       type: object
 *       properties:
 *         car:
 *           $ref: '#/components/schemas/Car'
 *         totalDays:
 *           type: number
 *           description: Number of booking days
 *         dailyRate:
 *           type: number
 *           description: Daily rental rate
 *         subtotal:
 *           type: number
 *           description: Subtotal before fees
 *         escortFee:
 *           type: number
 *           description: Escort service fee
 *         platformFee:
 *           type: number
 *           description: Platform fee
 *         totalAmount:
 *           type: number
 *           description: Total amount to pay
 *         netAmount:
 *           type: number
 *           description: Net amount after platform fee
 *         carToPickupDistanceKm:
 *           type: string
 *           description: Distance from car to pickup
 *         pickupToDestinationDistanceKm:
 *           type: string
 *           description: Distance from pickup to destination
 *         totalDistanceKm:
 *           type: string
 *           description: Total trip distance
 *
 *     ApproveBookingRequest:
 *       type: object
 *       required:
 *         - accept
 *       properties:
 *         accept:
 *           type: boolean
 *           description: Whether to approve or reject the booking
 *           example: true
 */

/**
 * @swagger
 * /bookings:
 *   post:
 *     summary: Create a new booking
 *     description: Create a new car rental booking
 *     tags: [Bookings]
 *     security:
 *       - clientAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBookingRequest'
 *     responses:
 *       201:
 *         description: Booking created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Booking'
 *       400:
 *         description: Invalid input data or car not available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - Client authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', authorizeClient, createBooking);

/**
 * @swagger
 * /bookings/preview:
 *   post:
 *     summary: Preview booking details
 *     description: Get booking preview with pricing and distance calculations before creating actual booking
 *     tags: [Bookings]
 *     security:
 *       - clientAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBookingRequest'
 *     responses:
 *       200:
 *         description: Booking preview generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BookingPreview'
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/preview', authorizeClient, previewBooking);

/**
 * @swagger
 * /bookings/approve/{id}:
 *   post:
 *     summary: Approve or reject booking
 *     description: Approve or reject a pending booking (admin/manager only)
 *     tags: [Booking Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ApproveBookingRequest'
 *     responses:
 *       200:
 *         description: Booking approval status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid input or booking cannot be approved
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Booking not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/approve/:id', authorize, restrictToRoles(['admin', 'manager']), approveBooking);

/**
 * @swagger
 * /bookings/rejected:
 *   get:
 *     summary: Get rejected bookings
 *     description: Retrieve all rejected bookings (super admin only)
 *     tags: [Booking Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Rejected bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Booking'
 *       401:
 *         description: Unauthorized - Super admin access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/declined', authorize, restrictToRoles(['super-admin']), getDeclinedBookings);

/**
 * @swagger
 * /bookings/{id}:
 *   patch:
 *     summary: Cancel booking
 *     description: Cancel an existing booking (client only)
 *     tags: [Bookings]
 *     security:
 *       - clientAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     responses:
 *       200:
 *         description: Booking cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Booking cannot be cancelled
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Booking not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch('/:id', authorizeClient, cancelBooking);

/**
 * @swagger
 * /bookings/manager/:
 *   get:
 *     summary: Get bookings for manager
 *     description: Retrieve bookings managed by the authenticated manager/admin
 *     tags: [Booking Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED, CANCELLED, COMPLETED]
 *         description: Filter by booking status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter bookings from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter bookings until this date
 *     responses:
 *       200:
 *         description: Manager bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Booking'
 *       401:
 *         description: Unauthorized - Manager/Admin access required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/manager/', authorize, restrictToRoles(['admin', 'manager']), getManagerBooking);

/**
 * @swagger
 * /bookings/client/:
 *   get:
 *     summary: Get client bookings
 *     description: Retrieve bookings for the authenticated client
 *     tags: [Bookings]
 *     security:
 *       - clientAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED, CANCELLED, COMPLETED]
 *         description: Filter by booking status
 *     responses:
 *       200:
 *         description: Client bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Booking'
 *       401:
 *         description: Unauthorized - Client authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/client/', authorizeClient, getClientBooking);

/**
 * @swagger
 * /bookings/{id}:
 *   get:
 *     summary: Get booking by ID
 *     description: Retrieve a specific booking by ID (accessible by client who made the booking or admin/manager)
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *       - clientAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Booking ID
 *     responses:
 *       200:
 *         description: Booking retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Booking'
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Booking not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  '/:id',
  async (req, res, next) => {
    try {
      // Try client auth first
      try {
        await authorizeClient(req, res, () => {}, true);
        return next();
      } catch (_) {
        // If client auth fails, try user auth
        try {
          await authorize(req, res, () => {}, true);
          return next();
        } catch (_) {
          return res.status(403).json({
            status: 'failed',
            message: 'Unauthorized access'
          });
        }
      }
    } catch (error) {
      next(error);
    }
  },
  restrictToRoles(['admin', 'manager', 'client']),
  getOneBooking
);

/**
 * @swagger
 * /bookings:
 *   get:
 *     summary: Get all bookings
 *     description: Retrieve all bookings (filtered by user role and permissions)
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *       - clientAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, APPROVED, REJECTED, CANCELLED, COMPLETED]
 *         description: Filter by booking status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter bookings from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter bookings until this date
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Booking'
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  '/',
  async (req, res, next) => {
    try {
      // Try client auth first
      try {
        await authorizeClient(req, res, () => {}, true);
        return next();
      } catch (_) {
        // If client auth fails, try user auth
        try {
          await authorize(req, res, () => {}, true);
          return next();
        } catch (_) {
          return res.status(403).json({
            status: 'failed',
            message: 'Unauthorized access'
          });
        }
      }
    } catch (error) {
      next(error);
    }
  },
  restrictToRoles(['admin', 'manager', 'client']),
  getRequesterBookings
);

router.get('/meta', authorize, restrictToRoles(['super-admin']), getBookingsMeta);

export default router;
