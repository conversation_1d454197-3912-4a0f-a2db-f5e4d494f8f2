import express from 'express';

import { createRating, deleteRating } from '../controllers/rating_controller.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create
router.post('/', authorizeClient, createRating);

// Delete
router.delete('/:id', authorize, restrictToRoles(['manager', 'admin']), deleteRating);

export default router;
