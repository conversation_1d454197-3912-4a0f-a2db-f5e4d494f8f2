import { Router } from 'express';

import advertRouter from './advertRoutes.js';
import authRouter from './authRoutes.js';
import bookingRouter from './bookingRoutes.js';
import brandRouter from './brandRoutes.js';
import carCategoryRouter from './carRoutes.js';
import clientRouter from './clientRoutes.js';
import indexRouter from './index.js';
import managersRouter from './managerRoutes.js';
import notificationRouter from './notificationRoutes.js';
import paymentRouter from './paymentRoutes.js';
import ratingRouter from './ratingRoutes.js';
import transactionsRouter from './transactionRoutes.js';
import usersRouter from './usersRoutes.js';
import walletRouter from './walletsRouter.js';

export type RouteConfig = {
  route: string;
  router: Router;
};

export const routerConfig: RouteConfig[] = [
  { route: '/', router: indexRouter },
  { route: '/users', router: usersRouter },
  { route: '/auth', router: authRouter },
  { route: '/manager', router: managersRouter },
  { route: '/brands', router: brandRouter },
  { route: '/cars', router: carCategoryRouter },
  { route: '/payments', router: paymentRouter },
  { route: '/bookings', router: bookingRouter },
  { route: '/clients', router: clientRouter },
  { route: '/adverts', router: advertRouter },
  { route: '/ratings', router: ratingRouter },
  { route: '/transaction', router: transactionsRouter },
  { route: '/notifications', router: notificationRouter },
  { route: '/wallets', router: walletRouter }
];
