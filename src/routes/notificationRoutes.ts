import express from 'express';

import {
  createNotification,
  deleteNotification,
  getClientNotification,
  getManagerNotification,
  updateNotifications
} from '../controllers/notification_controller.js';
import { authorizeClient } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create
router.post('/', createNotification);

// Fetch
router.get('/client', authorizeClient, getClientNotification);
router.get('/manager/:managerId', getManagerNotification);

// Update
router.patch('/:notificationId/read', updateNotifications);
router.patch('/', updateNotifications);

// Delete
router.delete('/:notificationId', deleteNotification);

export default router;
