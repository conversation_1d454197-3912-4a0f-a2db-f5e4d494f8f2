import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';
import { TRANSACTION_CHANEL, TRANSACTION_GATEWAY } from './transactions.js';

export type MandateDoc = {
  _id: Types.ObjectId;
  id: string;
  clientId: Types.ObjectId;
  country: string;
  gateway: TRANSACTION_GATEWAY;
  authorization: string;
  last4: string;
  accountName: string;
  bank: string;
  brand: string;
  channel: TRANSACTION_CHANEL;
  expiryMonth: string;
  expiryYear: string;
  countryCode: string;
  cardType: string;
  reusable: boolean;
  active: boolean;
  bin: string;
  signature: string;
  body: unknown;
  createdAt: Date;
  updatedAt: Date;
};

export type AdvancedMandateQueryResult = AdvancedQueryResult<MandateDoc>;
export type MandateDocExt = Omit<MandateDoc, 'createdBy' | 'updatedBy'> & {
  createdBy: { fullName: string };
  updatedBy?: { fullName: string };
};

export type RegisterTransactionRequestBody = Omit<MandateDoc, '_id' | 'createdAt' | 'updatedAt'>;
