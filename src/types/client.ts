import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type AddressDoc = {
  fullAddress: string;
  location: {
    type: string;
    coordinates: number[];
  };
  state: string;
  city: string;
  lga?: string;
  countryCode: string;
  country: string;
  postal: string;
  area: string;
  neighborhood: string;
  longitude: number;
  latitude: number;
};

export type OTPDoc = {
  otp: string;
  expiredAt: string;
};

export enum AUTH_CODES {
  AUTH_300 = 'Logged In Old user',
  AUTH_200 = 'Logged In New User',
  AUTH_400 = 'Device Mix Match'
}

export type ClientDoc = {
  _id: Types.ObjectId;
  id: string;
  email: string;
  password: string;
  deviceId?: string;
  fcmToken?: string;
  resetPasswordToken?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  phoneNumber: string;
  token?: OTPDoc;
  authToken?: string;
  address: AddressDoc;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  company: Types.ObjectId;
  matchPassword?: (enteredPassword: string) => Promise<boolean>;
  getSignedJwtToken?: () => string;
  oldPassword?: string;
  newPassword?: string;
};

export type SanitizedClientDoc = Omit<ClientDoc, 'password'>;

export type AdvancedClientQueryResult = AdvancedQueryResult<SanitizedClientDoc>;

export type RegisterClientRequestBody = Omit<ClientDoc, '_id' | 'createdAt' | 'updatedAt'>;
