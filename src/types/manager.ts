import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';
import { AddressDoc } from './user.js';

export type ManageDoc = {
  _id: Types.ObjectId;
  id: string;
  address: AddressDoc;
  companyName: string;
  supportEmail: string;
  supportPhone: string;
  walletBalance: number;
  currency: string;
  totalSpent: number;
  totalEarned: number;
  commission: number;
  user: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
};

export type AdvancedManagersQueryResult = AdvancedQueryResult<ManageDoc>;

export type RegisterManagerRequestBody = Omit<ManageDoc, '_id' | 'createdAt' | 'updatedAt' | 'password'>;
