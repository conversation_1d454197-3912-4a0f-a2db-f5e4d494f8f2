import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type RatingDoc = {
  _id: Types.ObjectId;
  id: string;
  clientId: Types.ObjectId;
  carId: Types.ObjectId;
  comment: string;
  rating: number;
  createdAt: Date;
  updatedAt: Date;
};

export type AdvancedRatingQueryResult = AdvancedQueryResult<RatingDoc>;

export type RegisterRatingRequestBody = Omit<RatingDoc, '_id' | 'createdAt' | 'updatedAt'>;
