import { Types } from 'mongoose';

export enum READSTATUS {
  SENT = 'sent',
  READ = 'read',
  DELIVERED = 'delivered'
}
export type NotificationDoc = {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  clientId: Types.ObjectId;
  id: string;
  title: string;
  body: string;
  action: string;
  readStatus: READSTATUS;
  isRead: boolean;
  isSent: boolean;
  fcmToken: string;
  createdAt: Date;
  updatedAt: Date;
};

export type RegisterNotificationRequestBody = Omit<NotificationDoc, '_id' | 'createdAt' | 'updatedAt'>;
