import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';
import { AddressDoc } from './user.js';

export enum BOOKING_STATUS {
  PENDING = 'pending',
  BOOKED = 'booked',
  CANCELED = 'canceled',
  IN_PROGRESS = 'inProgress',
  COMPLETED = 'completed'
}

export enum APPROVAL_STATUS {
  PENDING = 'pending',
  APPROVED = 'approved',
  DECLINED = 'declined'
}

export enum BOOKING_TYPE {
  STANDARD = 'standard',
  COMBO = 'combo'
}

export type BookingDoc = {
  _id: Types.ObjectId;
  id: string;
  clientId: Types.ObjectId; // client booking the car
  companyId: Types.ObjectId;
  managerId: Types.ObjectId;
  car: Types.ObjectId;
  carId: string;
  startDate: string;
  startTime: string;
  endDate: string;
  status: BOOKING_STATUS;
  approvalStatus: APPROVAL_STATUS;
  bookingType: BOOKING_TYPE;
  pickupAddress: AddressDoc;
  destinationAddress: AddressDoc;
  totalPrice: number; // number of days + escort count + other calculations
  managerTotal: number;
  escortsTotal: number;
  hasAdditionalStop: boolean;
  numberOfDays: number; // cal of startDate and endDate
  vat: number;
  escortCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  pickupKilometer?: number;
  destinationKilometer?: number;
  bookingCharges?: number;
  escortDays?: number;
  bookingReference?: string;
};

export type AdvancedBookingQueryResult = AdvancedQueryResult<BookingDoc>;
export type BookingDocExt = Omit<BookingDoc, 'createdBy' | 'updatedBy'> & {
  createdBy: { fullName: string };
  updatedBy?: { fullName: string };
};

export type RegisterBookingRequestBody = Omit<BookingDoc, '_id' | 'createdAt' | 'updatedAt'>;
