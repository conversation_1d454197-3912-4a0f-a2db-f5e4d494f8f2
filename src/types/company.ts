import { Types } from 'mongoose';

import { PaymentInfoDoc } from './payment_info.js';
import { AdvancedQueryResult } from './query_results.js';
import { AddressDoc } from './user.js';

export type CompanyDoc = {
  _id: Types.ObjectId;
  user: Types.ObjectId;
  id: string;
  email: string;
  name: string;
  phoneNumber: string;
  address: AddressDoc;
  createdAt: Date;
  updatedAt: Date;
  paymentInfo: PaymentInfoDoc | Types.ObjectId;
  isActive: boolean;
};

export type AdvancedUsersQueryResult = AdvancedQueryResult<CompanyDoc>;

export type RegisterUserRequestBody = Omit<CompanyDoc, '_id' | 'createdAt' | 'updatedAt'>;
