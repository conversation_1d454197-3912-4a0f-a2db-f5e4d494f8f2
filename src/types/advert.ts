import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type AdvertDoc = {
  _id: Types.ObjectId;
  id: string;
  company: Types.ObjectId;
  car: Types.ObjectId;
  startDate: string;
  endDate: string;
  isApproved: boolean;
  advertType: string;
  spentBudget: string;
  targetViews: string;
  targetClicks: string;
  totalViews: string;
  totalClicks: string;
  budgetCap: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
};

export type AdvancedAdvertQueryResult = AdvancedQueryResult<AdvertDoc>;

export type RegisterAdvertRequestBody = Omit<AdvertDoc, '_id' | 'createdAt' | 'updatedAt'>;
