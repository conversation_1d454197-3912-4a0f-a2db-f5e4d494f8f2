import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type BookedDatesDoc = {
  _id: Types.ObjectId;
  id: string;
  car: Types.ObjectId;
  bookingId: Types.ObjectId;
  startDate: string;
  startTime: string;
  endDate: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
};

export type AdvancedAdvertQueryResult = AdvancedQueryResult<BookedDatesDoc>;

export type RegisterAdvertRequestBody = Omit<BookedDatesDoc, '_id' | 'createdAt' | 'updatedAt'>;
