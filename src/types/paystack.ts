import { TRANSACTION_CHANEL } from './transactions.js';

export type PaystackLogHistory = {
  type: string;
  message: string;
  time: number;
};

export type PaystackLog = {
  start_time: number;
  time_spent: number;
  attempts: number;
  errors: number;
  success: boolean;
  mobile: boolean;
  input: any[];
  history: PaystackLogHistory[];
};

export type PaystackAuthorization = {
  authorization_code: string;
  bin: string;
  last4: string;
  exp_month: string;
  exp_year: string;
  channel: string;
  card_type: string;
  bank: string;
  country_code: string;
  brand: string;
  reusable: boolean;
  signature: string;
  account_name: string | null;
};

export type PaystackCustomer = {
  id: number;
  first_name: string | null;
  last_name: string | null;
  email: string;
  customer_code: string;
  phone: string | null;
  metadata: any;
  risk_action: string;
  international_format_phone: string | null;
};

export type PaystackTransactionData = {
  id: number;
  domain: string;
  status: string;
  reference: string;
  receipt_number: string | null;
  amount: number;
  message: string | null;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  // channel: string;
  channel: TRANSACTION_CHANEL;
  currency: string;
  ip_address: string;
  metadata: any;
  log: PaystackLog;
  fees: number;
  fees_split: any;
  authorization: PaystackAuthorization;
  customer: PaystackCustomer;
  plan: any;
  split: any;
  order_id: string | null;
  paidAt: string;
  createdAt: string;
  requested_amount: number;
  pos_transaction_data: any;
  source: any;
  fees_breakdown: any;
  connect: any;
  transaction_date: string;
  plan_object: any;
  subaccount: any;
};

export type PaystackResponse = {
  status: boolean;
  message: string;
  data: PaystackTransactionData;
};

export type PaystackInitDocs = {
  status: boolean;
  message: string;
  data: {
    authorization_url: string;
    access_code: string;
    reference: string;
  };
};

// export type AuthorizationDoc = {
//   authorization_code: string;
//   bin: string;
//   last4: string;
//   exp_month: string;
//   exp_year: string;
//   channel: string;
//   card_type: string;
//   bank: string;
//   country_code: string;
//   brand: string;
//   reusable: string;
//   signature: string;
//   account_name: string;
// };

// export type VerifyDoc = {
//   channel: TRANSACTION_CHANEL;
//   message: string;
//   fees: string;
//   amount: string;
//   status: string;
//   authorization: AuthorizationDoc;
// };
