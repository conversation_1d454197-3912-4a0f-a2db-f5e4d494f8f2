import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type PaymentInfoDoc = {
  _id: Types.ObjectId;
  id: string;
  accountName: string;
  bankName: string;
  accountNumber: string;
  email: string;
  company: Types.ObjectId;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
};

export type AdvancedPaymentQueryResult = AdvancedQueryResult<PaymentInfoDoc>;
export type RegisterPaymentInfoRequestBody = Omit<
  PaymentInfoDoc,
  '_id' | 'id' | 'createdAt' | 'updatedAt' | 'isActive' | 'company' | 'createdBy' | 'updatedBy'
>;
