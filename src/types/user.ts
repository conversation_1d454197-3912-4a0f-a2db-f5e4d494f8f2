import { Types } from 'mongoose';

import { CompanyDoc } from './company.js';
import { RegisterPaymentInfoRequestBody } from './payment_info.js';
import { AdvancedQueryResult } from './query_results.js';

export type AddressDoc = {
  fullAddress: string;
  location: {
    type: string;
    coordinates: number[]; // Longitude first, then Latitude
  };
  state: string;
  city: string;
  lga?: string;
  countryCode: string;
  country: string;
  postal: string;
  area: string;
  neighborhood: string;
  longitude: number;
  latitude: number;
};

export type OTPDoc = {
  otp: string;
  expiredAt: string;
};

export enum USER_ROLE {
  ADMIN = 'admin',
  USER = 'user',
  MANAGER = 'manager',
  SUPER_ADMIN = 'super-admin',
  SUB_MANAGER = 'sub-manager',
  CLIENT = 'client'
}

export type UserDoc = {
  _id: Types.ObjectId;
  id: string;
  email: string;
  password: string;
  deviceId?: string;
  fcmToken?: string;
  resetPasswordToken?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  phoneNumber: string;
  token?: OTPDoc;
  authToken?: string;
  role: USER_ROLE;
  address: AddressDoc;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  company: Types.ObjectId;
  paymentInfo?: RegisterPaymentInfoRequestBody;
  matchPassword?: (enteredPassword: string) => Promise<boolean>;
  getSignedJwtToken?: () => string;
  oldPassword?: string;
  newPassword?: string;
};

export type SanitizedUserDoc = Omit<UserDoc, 'password' | 'isActive' | 'token' | 'resetPasswordToken'>;

export type AdvancedUsersQueryResult = AdvancedQueryResult<SanitizedUserDoc>;

export type RegisterUserRequestBody = Omit<UserDoc, '_id' | 'createdAt' | 'updatedAt'>;

export type RegisterCompanyRequestBody = Omit<CompanyDoc, '_id' | 'createdAt' | 'updatedAt'> & UserDoc;
