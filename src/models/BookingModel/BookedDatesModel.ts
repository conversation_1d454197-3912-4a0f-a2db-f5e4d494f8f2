import mongoose, { Model, Schema } from 'mongoose';

import { AdvertDoc } from '../../types/advert.js';
import { BookedDatesDoc } from '../../types/booking_dates.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type BookingDatesDocumentResult = BookedDatesDoc & BaseDocument<BookedDatesDoc>;

type AdvertModel = BaseModelMethods<BookingDatesDocumentResult> & Model<AdvertDoc>;

const bookedDateSchema = new mongoose.Schema<BookingDatesDocumentResult, AdvertModel>(
  {
    car: {
      type: Schema.Types.ObjectId,
      ref: 'Car',
      required: true
    },
    bookingId: {
      type: Schema.Types.ObjectId,
      ref: 'Booking',
      required: true
    },
    startDate: {
      type: String
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

bookedDateSchema.static('findOneActive', findOneActive);
bookedDateSchema.static('findActive', findActive);
bookedDateSchema.static('findAndPopulate', findAndPopulate);
bookedDateSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const BookedDates = mongoose.model<BookingDatesDocumentResult, AdvertModel>('BookedDates', bookedDateSchema);
export default BookedDates;
