import mongoose, { Model, Schema } from 'mongoose';

import { ManageDoc } from '../../types/manager.js';
import { AddressDoc } from '../../types/user.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type ManagerDocumentResult = ManageDoc & BaseDocument<ManageDoc>;

type ManagerModel = BaseModelMethods<ManagerDocumentResult> & Model<ManageDoc>;

export const addressSchema = new mongoose.Schema<AddressDoc>({
  fullAddress: { type: String, required: true },
  state: { type: String, required: true },
  city: { type: String, required: true },
  lga: { type: String, required: true },
  countryCode: { type: String, required: true },
  country: { type: String, required: true },
  neighborhood: { type: String, required: true },
  area: { type: String, required: true },
  longitude: { type: Number, required: true },
  latitude: { type: Number, required: true }
});

const managerSchema = new mongoose.Schema<ManagerDocumentResult, ManagerModel>(
  {
    supportEmail: {
      type: String,
      required: true,
      unique: true,
      lowercase: true
    },
    address: {
      type: addressSchema,
      required: true
    },
    companyName: {
      type: String,
      required: true
    },
    supportPhone: {
      type: String,
      required: true,
      unique: true
    },
    walletBalance: {
      type: Number
    },
    totalSpent: {
      type: Number
    },
    totalEarned: {
      type: Number
    },
    commission: {
      type: Number
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    currency: {
      type: String
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

managerSchema.static('findOneActive', findOneActive);
managerSchema.static('findActive', findActive);
managerSchema.static('findAndPopulate', findAndPopulate);
managerSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Manager = mongoose.model<ManagerDocumentResult, ManagerModel>('Manager', managerSchema);
export default Manager;
