import mongoose, { Model, Schema } from 'mongoose';

import { ManageDoc } from '../../types/manager.js';
import { AddressDoc } from '../../types/user.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';
import { CompanyDoc } from './../../types/company.js';

type BaseDocument<T> = {
  _doc: T;
};

export type CompanyDocumentResult = CompanyDoc & BaseDocument<ManageDoc>;

type CompanyModel = BaseModelMethods<CompanyDocumentResult> & Model<ManageDoc>;

export const addressSchema = new mongoose.Schema<AddressDoc>({
  fullAddress: { type: String, required: true },
  location: {
    type: { type: String, enum: ['Point'], required: true, default: 'Point' },
    coordinates: {
      type: [Number], // Longitude first, then Latitude
      required: true
    }
  },
  state: { type: String, required: true },
  longitude: { type: Number, required: true },
  latitude: { type: Number, required: true },
  city: { type: String, required: true },
  countryCode: { type: String, required: true },
  country: { type: String, required: true },
  lga: { type: String },
  postal: { type: String },
  area: { type: String },
  neighborhood: { type: String }
});

addressSchema.index({ location: '2dsphere' });

const companySchema = new mongoose.Schema<CompanyDocumentResult, CompanyModel>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true
    },
    address: {
      type: addressSchema,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    phoneNumber: {
      type: String,
      required: true,
      unique: true
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    paymentInfo: {
      type: Schema.Types.ObjectId,
      ref: 'PaymentInfo'
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

companySchema.static('findOneActive', findOneActive);
companySchema.static('findActive', findActive);
companySchema.static('findAndPopulate', findAndPopulate);
companySchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Company = mongoose.model<CompanyDocumentResult, CompanyModel>('Company', companySchema);
export default Company;
