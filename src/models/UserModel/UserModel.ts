import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import mongoose, { Model, Schema, UpdateQuery } from 'mongoose';

import { OTPDoc, USER_ROLE, UserDoc } from '../../types/user.js';
import { addressSchema } from '../CompanyModel/CompanyModel.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type UserDocumentResult = UserDoc & BaseDocument<UserDoc>;

type UserModel = BaseModelMethods<UserDocumentResult> & Model<UserDoc>;

export const otpSchema = new mongoose.Schema<OTPDoc>({
  otp: { type: String, required: true },
  expiredAt: { type: String, required: true }
});

const userSchema = new mongoose.Schema<UserDocumentResult, UserModel>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true
    },
    address: {
      type: addressSchema,
      required: true
    },
    firstName: {
      type: String
    },
    middleName: {
      type: String
    },
    password: {
      type: String,
      required: true
    },
    lastName: {
      type: String
    },
    phoneNumber: {
      type: String,
      required: true,
      unique: true
    },
    deviceId: {
      type: String
    },
    fcmToken: {
      type: String
    },
    token: {
      type: otpSchema
    },
    authToken: {
      type: String
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'Company'
    },
    role: {
      type: String,
      enum: USER_ROLE
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

userSchema.pre('save', async function (next) {
  if (this.isNew || this.isModified('password')) {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, await bcrypt.genSalt(10));
    }
  }
  next();
});

userSchema.pre('findOneAndUpdate', async function () {
  const update: UpdateQuery<UserDocumentResult> = this.getUpdate();
  if (update.password && typeof update.password === 'string') {
    update.password = await bcrypt.hash(update.password, await bcrypt.genSalt(10));
  }
});

userSchema.methods.matchPassword = async function (this: UserDoc, enteredPassword: string) {
  return await bcrypt.compare(enteredPassword, this.password);
};

userSchema.methods.getSignedJwtToken = function (this: UserDoc) {
  const JWT_SECRET = process.env.JWT_SECRET;
  return jwt.sign({ email: this.email, role: this.role, password: this.password }, JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
};

userSchema.static('findOneActive', findOneActive);
userSchema.static('findActive', findActive);
userSchema.static('findAndPopulate', findAndPopulate);
userSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const User = mongoose.model<UserDocumentResult, UserModel>('User', userSchema);
export default User;
