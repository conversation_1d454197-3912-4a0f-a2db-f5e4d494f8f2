import { formatNigerianPhoneNumber } from '../helpers/user_helper.js';
import Logger from '../libs/logger.js';
import User from '../models/UserModel/UserModel.js';
import Brand from './BrandModel/BrandModel.js';
import CarCategory from './CarCategoryModel/CarCategoryModel.js';

const userSeedData = [
  {
    _id: '66056a0b8cddbeac52b7221f',
    email: '<EMAIL>',
    password: '111222333',
    role: 'admin',
    phoneNumber: formatNigerianPhoneNumber('070687983458'),
    firstName: 'Okhai',
    middleName: 'Okahi',
    lastName: 'Okahi',
    fcmToken: 'LS1CRUdJTiBSU0WEIOJklsdlopjsd',
    deviceId: 'erprpfklkmnsdiojweoilksjhfhf',
    address: {
      location: {
        type: 'Point',
        coordinates: [7.4692, 9.0323]
      },
      fullAddress: 'area 2 garki Abuja',
      longitude: 9.0323,
      latitude: 7.4692,
      state: 'Abuja FCT',
      city: 'Abuja',
      lga: 'AMAC',
      countryCode: 'NG',
      country: 'Nigeria',
      neighborhood: 'Area 2'
    },
    createdBy: '66056a0b8cddbeac52b7221f'
  }
];

const adminUserSeedData = [
  {
    _id: '66ed6de033bb20379bd61457',
    email: '<EMAIL>',
    password: '111222333',
    role: 'super-admin',
    phoneNumber: formatNigerianPhoneNumber('07067874038'),
    firstName: 'Super',
    middleName: 'Admin',
    lastName: 'Super',
    fcmToken: 'LS1CRUdJTiBSU0WEIOJklsdlopjsd',
    deviceId: 'erprpfklkmnsdiojweoilksjhfhf',
    address: {
      location: {
        type: 'Point',
        coordinates: [6.4703, 3.2818]
      },
      fullAddress: 'area 1 garki Abuja',
      longitude: 9.0323,
      latitude: 7.4692,
      state: 'Abuja FCT',
      city: 'Abuja',
      lga: 'AMAC',
      countryCode: 'NG',
      country: 'Nigeria',
      neighborhood: 'Area 2'
    },
    createdBy: '66056a0b8cddbeac52b7221f'
  }
];

export const fleetSeedData = [
  {
    _id: '67056a0b7ceebeac52b7331f',
    email: '<EMAIL>',
    password: '111222333',
    role: 'manager',
    phoneNumber: formatNigerianPhoneNumber('070688983123'),
    firstName: 'Bonku',
    middleName: 'Ogie',
    lastName: 'Okahi',
    fcmToken: 'LS1CRUdJTiBSU0WEI',
    deviceId: 'erprpfklkmnsdioj',
    address: {
      location: {
        type: 'Point',
        coordinates: [7.4692, 9.0323]
      },
      fullAddress: 'Area 2 garki Abuja',
      longitude: 9.0323,
      latitude: 7.4692,
      state: 'Abuja FCT',
      city: 'Abuja',
      lga: 'AMAC',
      countryCode: 'NG',
      country: 'Nigeria',
      neighborhood: 'Area 2'
    },
    createdBy: '66056a0b8cddbeac52b7221f'
  }
];

const brandSeedData = [
  {
    _id: '66e950ed621de319297b242e',
    name: 'Toyota'
  },
  {
    _id: '66e950fb621de319297b2431',
    name: 'Rolls Royce'
  },
  {
    _id: '66e95104621de319297b2434',
    name: 'Lexus'
  },
  {
    _id: '66e9510c621de319297b2437',
    name: 'Mercedes-Benz'
  },
  {
    _id: '66e95115621de319297b243a',
    name: 'Ford'
  },
  {
    _id: '66e9511b621de319297b243d',
    name: 'Chevrolet'
  },
  {
    _id: '66e95120621de319297b2440',
    name: 'Dodge'
  },
  {
    _id: '66e95129621de319297b2443',
    name: 'BMW'
  },
  {
    _id: '66e9512e621de319297b2446',
    name: 'Audi'
  },
  {
    _id: '66e95136621de319297b2449',
    name: 'Lincoln'
  },
  {
    _id: '66e9513b621de319297b244c',
    name: 'Cadillac'
  },
  {
    _id: '66e95141621de319297b244f',
    name: 'Chrysler'
  },
  {
    _id: '66e95147621de319297b2452',
    name: 'IVM'
  }
];

const carCategoriesSeedData = [
  {
    _id: '66e95071621de319297b2422',
    name: 'Sports',
    pictureUrl: 'https://cdn.motor1.com/images/mgl/P33WYL/s1/ferrari-sp48-unica.jpg'
  },
  {
    _id: '66e9508f621de319297b2425',
    name: 'Wedding',
    pictureUrl:
      'https://c8.alamy.com/comp/2APRPNE/brussels-jan-9-2020-new-bentley-flying-spur-luxury-car-model-showcased-at-the-brussels-autosalon-2020-motor-show-2APRPNE.jpg'
  },
  {
    _id: '66e9509b621de319297b2428',
    name: 'SUV',
    pictureUrl:
      'https://media.istockphoto.com/id/1148058147/photo/red-generic-suv-on-black-background.jpg?s=612x612&w=0&k=20&c=sEmgHihgWytks495jWLHz1hy6xfz5S_9PXntiUn8UDE='
  },
  {
    _id: '66e950aa621de319297b242b',
    name: 'Sedan',
    isActive: true,
    pictureUrl:
      'https://media.istockphoto.com/id/1185460602/photo/3d-illustration-of-generic-red-sedan-car-front-side-view.jpg?s=612x612&w=0&k=20&c=bi8lRPp8xK_oFTDhkWQLk0I0LdiCgzcffXJnwAbSmR0='
  }
];

export const seedEssentialData = async () => {
  try {
    const brand = await Brand.find({});
    const carCategories = await CarCategory.find({});

    if (!brand.length) {
      await Brand.create(brandSeedData);
      Logger.info('Brands seeded successfully');
    }

    if (!carCategories.length) {
      await CarCategory.create(carCategoriesSeedData);
      Logger.info('Car categories seeded successfully');
    }
  } catch (error) {
    Logger.error('Error seeding essential data:', error);
    throw error;
  }
};

export const seedDevelopmentData = async () => {
  try {
    // Check for existing users by their IDs
    const existingUserIds = await User.find({ _id: { $in: userSeedData.map(u => u._id) } }).select('_id');
    const existingAdminIds = await User.find({ _id: { $in: adminUserSeedData.map(u => u._id) } }).select('_id');
    const existingFleetIds = await User.find({ _id: { $in: fleetSeedData.map(u => u._id) } }).select('_id');

    // Filter out existing users
    const newUsers = userSeedData.filter(u => !existingUserIds.some(eu => eu._id.toString() === u._id));
    const newAdmins = adminUserSeedData.filter(u => !existingAdminIds.some(eu => eu._id.toString() === u._id));
    const newFleet = fleetSeedData.filter(u => !existingFleetIds.some(eu => eu._id.toString() === u._id));

    if (newUsers.length > 0) {
      await User.create(newUsers);
      Logger.info('Admin users seeded successfully');
    }

    if (newFleet.length > 0) {
      await User.create(newFleet);
      Logger.info('Fleet users seeded successfully');
    }

    if (newAdmins.length > 0) {
      await User.create(newAdmins);
      Logger.info('Super admin users seeded successfully');
    }
  } catch (error) {
    Logger.error('Error seeding development data:', error);
    throw error;
  }
};

export const seedDBdata = async () => {
  try {
    // Always seed essential data (brands and categories)
    await seedEssentialData();

    // Only seed development data in development environment
    if (process.env.NODE_ENV === 'development') {
      await seedDevelopmentData();
    }
  } catch (error) {
    Logger.error('Error in database seeding:', error);
    throw error;
  }
};
