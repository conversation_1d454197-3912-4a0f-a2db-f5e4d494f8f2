import mongoose, { Model, Schema } from 'mongoose';

import { BookDatesType, CarDoc, ENGINE_TYPE, SCHEDULE } from '../../types/car.js';
import { addressSchema } from '../CompanyModel/CompanyModel.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type CarDocumentResult = CarDoc & BaseDocument<CarDoc>;

type CarModel = BaseModelMethods<CarDocumentResult> & Model<CarDoc>;

export const bookedDateSchema = new mongoose.Schema<BookDatesType>({
  startDate: { type: String, required: true },
  endDate: { type: String, required: true },
  startTime: { type: String, required: true }
});

const carSchema = new mongoose.Schema<CarDocumentResult, CarModel>(
  {
    description: {
      type: String
    },
    engineType: {
      type: String,
      enum: ENGINE_TYPE
    },
    schedule: {
      type: String,
      enum: SCHEDULE
    },
    address: {
      type: addressSchema,
      required: true
    },
    year: {
      type: String,
      required: true
    },
    dailyPrice: {
      type: Number,
      required: true
    },
    dailyMinPrice: {
      type: Number,
      required: true
    },
    seatType: {
      type: String
    },
    model: {
      type: String
    },
    carModel: {
      type: Schema.Types.ObjectId,
      ref: 'Model'
    },
    rating: [
      {
        type: Schema.Types.ObjectId
      }
    ],
    ratingSummary: {
      type: Number,
      min: 1,
      max: 10,
      required: true,
      default: 5
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true
    },
    brand: {
      type: Schema.Types.ObjectId,
      ref: 'Brand',
      required: true
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: 'CarCategory',
      required: true
    },
    carImages: [
      {
        type: Schema.Types.ObjectId,
        ref: 'CarImage'
      }
    ],
    bookingCount: {
      type: Number,
      default: 0
    },
    totalEarned: {
      type: Number,
      default: 0
    },
    managerTotalEarned: {
      type: Number,
      default: 0
    },
    isAvailable: {
      type: Boolean,
      default: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    isActive: {
      type: Boolean,
      default: true
    },
    bookedDates: [
      {
        type: bookedDateSchema
      }
    ],
    numOfDoors: {
      type: Number
    }
  },
  {
    timestamps: true
  }
);

carSchema.static('findOneActive', findOneActive);
carSchema.static('findActive', findActive);
carSchema.static('findAndPopulate', findAndPopulate);
carSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Car = mongoose.model<CarDocumentResult, CarModel>('Car', carSchema);
export default Car;
