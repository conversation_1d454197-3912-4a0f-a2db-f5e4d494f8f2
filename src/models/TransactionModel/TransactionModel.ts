import mongoose, { Model, Schema } from 'mongoose';

import {
  TRANSACTION_CHANEL,
  TRANSACTION_GATEWAY,
  TRANSACTION_STATUS,
  TRANSACTION_TYPE,
  TransactionDoc
} from '../../types/transactions.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type TransactionDocumentResult = TransactionDoc & BaseDocument<TransactionDoc>;

type TransactionModel = BaseModelMethods<TransactionDocumentResult> & Model<TransactionDoc>;

const transactionSchema = new mongoose.Schema<TransactionDocumentResult, TransactionModel>(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      ref: 'Client'
    },
    companyId: {
      type: Schema.Types.ObjectId,
      ref: 'Company'
    },
    bookingId: {
      type: Schema.Types.ObjectId,
      ref: 'Booking',
      required: true
    },
    channel: {
      type: String,
      enum: TRANSACTION_CHANEL
    },
    description: { type: String },
    country: {
      name: { type: String },
      code: { type: String },
      currency: { type: String }
    },
    auth: {
      type: String
    },
    email: {
      type: String
    },
    message: {
      type: String
    },
    amount: {
      type: Number
    },
    checkoutUrl: {
      type: String
    },
    charge: {
      type: Number,
      default: 0
    },
    reference: {
      type: String,
      unique: true
    },
    merchantReference: {
      type: String
    },
    meta: {
      type: Object
    },
    extra: {
      type: Object
    },
    gateway: {
      type: String,
      enum: TRANSACTION_GATEWAY
    },
    status: {
      type: String,
      default: TRANSACTION_STATUS.ATTEMPTED,
      enum: TRANSACTION_STATUS
    },
    type: {
      type: String,
      enum: TRANSACTION_TYPE
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

transactionSchema.static('findOneActive', findOneActive);
transactionSchema.static('findActive', findActive);
transactionSchema.static('findAndPopulate', findAndPopulate);
transactionSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Transaction = mongoose.model<TransactionDocumentResult, TransactionModel>('Transaction', transactionSchema);
export default Transaction;
