import mongoose, { Model, Schema } from 'mongoose';

import { MandateDoc } from '../../types/mandate.js';
import { BaseModelMethods } from '../methods/methods.js';
import { TRANSACTION_GATEWAY } from './../../types/transactions.js';

type BaseDocument<T> = {
  _doc: T;
};

export type MandateDocumentResult = MandateDoc & BaseDocument<MandateDoc>;

type MandateModel = BaseModelMethods<MandateDocumentResult> & Model<MandateDoc>;

const mandateSchema = new mongoose.Schema<MandateDocumentResult, MandateModel>(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      ref: 'Client',
      required: true
    },
    country: {
      type: String
    },
    gateway: {
      type: String,
      enum: TRANSACTION_GATEWAY
    },
    authorization: {
      type: String
    },
    last4: {
      type: String
    },
    accountName: {
      type: String
    },
    bank: {
      type: String
    },
    brand: {
      type: String
    },
    channel: {
      type: String
    },
    expiryMonth: {
      type: String
    },
    expiryYear: {
      type: String
    },
    countryCode: {
      type: String
    },
    cardType: {
      type: String
    },
    reusable: {
      type: Boolean
    },
    active: {
      type: Boolean
    },
    bin: {
      type: String
    },
    signature: {
      type: String
    },
    body: {
      type: mongoose.Schema.Types.Mixed
    }
  },
  { timestamps: true }
);

const Mandate = mongoose.model<MandateDocumentResult, MandateModel>('Mandate', mandateSchema);
export default Mandate;
