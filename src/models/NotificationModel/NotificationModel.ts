import mongoose, { Model, Schema } from 'mongoose';

import { NotificationDoc } from '../../types/notification.js';
import { BaseModelMethods } from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type NotificationDocumentResult = NotificationDoc & BaseDocument<NotificationDoc>;

type PaymentInfoModel = BaseModelMethods<NotificationDocumentResult> & Model<NotificationDoc>;

const notificationSchema = new mongoose.Schema<NotificationDocumentResult, PaymentInfoModel>(
  {
    title: {
      type: String
    },
    body: {
      type: String
    },
    action: {
      type: String
    },
    fcmToken: {
      type: String
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    clientId: {
      type: Schema.Types.ObjectId,
      ref: 'Client'
    },
    isRead: {
      type: Boolean,
      default: false
    },
    isSent: {
      type: Boolean
    }
  },
  {
    timestamps: true
  }
);

const Notification = mongoose.model<NotificationDocumentResult, PaymentInfoModel>('Notification', notificationSchema);
export default Notification;
