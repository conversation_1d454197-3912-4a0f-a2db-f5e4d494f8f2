import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import mongoose, { Model, UpdateQuery } from 'mongoose';

import { ClientDoc } from '../../types/client.js';
import { OTPDoc } from '../../types/user.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type ClientDocumentResult = ClientDoc & BaseDocument<ClientDoc>;

type ClientModel = BaseModelMethods<ClientDocumentResult> & Model<ClientDoc>;

export const otpSchema = new mongoose.Schema<OTPDoc>({
  otp: { type: String, required: true },
  expiredAt: { type: String, required: true }
});

const clientSchema = new mongoose.Schema<ClientDocumentResult, ClientModel>(
  {
    email: {
      type: String,
      // unique: true, // No unique here; we will create a partial index instead
      lowercase: true
    },
    firstName: {
      type: String
    },
    middleName: {
      type: String
    },
    password: {
      type: String
    },
    lastName: {
      type: String
    },
    phoneNumber: {
      type: String,
      required: true,
      unique: true
    },
    deviceId: {
      type: String
    },
    fcmToken: {
      type: String
    },
    token: {
      type: otpSchema
    },
    authToken: {
      type: String
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

// Create a partial unique index on email
clientSchema.index(
  { email: 1 },
  { unique: true, partialFilterExpression: { email: { $exists: true, $nin: [null, ''] } } }
);

clientSchema.pre('save', async function (next) {
  if (this.isNew || this.isModified('password')) {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, await bcrypt.genSalt(10));
    }
  }
  next();
});

clientSchema.pre('findOneAndUpdate', async function () {
  const update: UpdateQuery<ClientDocumentResult> = this.getUpdate();
  if (update.password && typeof update.password === 'string') {
    update.password = await bcrypt.hash(update.password, await bcrypt.genSalt(10));
  }
});

clientSchema.methods.matchPassword = async function (this: ClientDoc, enteredPassword: string) {
  return await bcrypt.compare(enteredPassword, this.password);
};

clientSchema.methods.getSignedJwtToken = function (this: ClientDoc) {
  const JWT_SECRET = process.env.JWT_SECRET;
  return jwt.sign({ phoneNumber: this.phoneNumber }, JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
};

clientSchema.static('findOneActive', findOneActive);
clientSchema.static('findActive', findActive);
clientSchema.static('findAndPopulate', findAndPopulate);
clientSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Client = mongoose.model<ClientDocumentResult, ClientModel>('Client', clientSchema);
export default Client;
