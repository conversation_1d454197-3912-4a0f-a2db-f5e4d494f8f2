import mongoose, { Model } from 'mongoose';

import { BrandDoc } from '../../types/brand.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type BrandDocumentResult = BrandDoc & BaseDocument<BrandDoc>;

type CarModel = BaseModelMethods<BrandDocumentResult> & Model<BrandDoc>;

const brandSchema = new mongoose.Schema<BrandDocumentResult, CarModel>(
  {
    name: {
      type: String,
      required: true,
      unique: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

brandSchema.static('findOneActive', findOneActive);
brandSchema.static('findActive', findActive);
brandSchema.static('findAndPopulate', findAndPopulate);
brandSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Brand = mongoose.model<BrandDocumentResult, CarModel>('Brand', brandSchema);
export default Brand;
