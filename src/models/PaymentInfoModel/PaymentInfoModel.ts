import mongoose, { Model, Schema } from 'mongoose';

import { PaymentInfoDoc } from '../../types/payment_info.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type PaymentInfoDocumentResult = PaymentInfoDoc & BaseDocument<PaymentInfoDoc>;

type PaymentInfoModel = BaseModelMethods<PaymentInfoDocumentResult> & Model<PaymentInfoDoc>;

const paymentInfoSchema = new mongoose.Schema<PaymentInfoDocumentResult, PaymentInfoModel>(
  {
    bankName: {
      type: String,
      required: true
    },
    accountName: {
      type: String,
      required: true
    },
    accountNumber: {
      type: String,
      required: true
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

paymentInfoSchema.static('findOneActive', findOneActive);
paymentInfoSchema.static('findActive', findActive);
paymentInfoSchema.static('findAndPopulate', findAndPopulate);
paymentInfoSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const PaymentInfo = mongoose.model<PaymentInfoDocumentResult, PaymentInfoModel>('PaymentInfo', paymentInfoSchema);
export default PaymentInfo;
