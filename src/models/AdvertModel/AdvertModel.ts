import mongoose, { Model, Schema } from 'mongoose';

import { AdvertDoc } from '../../types/advert.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type AdvertDocumentResult = AdvertDoc & BaseDocument<AdvertDoc>;

type AdvertModel = BaseModelMethods<AdvertDocumentResult> & Model<AdvertDoc>;
/* 
  
  : boolean;
  : string;
  : string;
  : string;
  : string;
  : string;
  : string;
  : string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
*/
const advertSchema = new mongoose.Schema<AdvertDocumentResult, AdvertModel>(
  {
    car: {
      type: Schema.Types.ObjectId,
      ref: 'Car',
      required: true
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true
    },
    startDate: {
      type: String,
      required: true
    },
    endDate: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    isApproved: {
      type: Boolean,
      default: true
    },
    advertType: {
      type: String
    },
    spentBudget: {
      type: String
    },
    targetViews: {
      type: String
    },
    targetClicks: {
      type: String
    },
    totalViews: {
      type: String
    },
    totalClicks: {
      type: String
    },
    budgetCap: {
      type: String
    }
  },
  {
    timestamps: true
  }
);

advertSchema.static('findOneActive', findOneActive);
advertSchema.static('findActive', findActive);
advertSchema.static('findAndPopulate', findAndPopulate);
advertSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const Advert = mongoose.model<AdvertDocumentResult, AdvertModel>('Advert', advertSchema);
export default Advert;
