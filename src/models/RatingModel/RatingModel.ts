import mongoose, { Model, Schema } from 'mongoose';

import { AdvertDoc } from '../../types/advert.js';
import { RatingDoc } from '../../types/rating.js';
import { BaseModelMethods } from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type RatingDocumentResult = RatingDoc & BaseDocument<RatingDoc>;

type RatingModel = BaseModelMethods<RatingDocumentResult> & Model<AdvertDoc>;

const ratingSchema = new mongoose.Schema<RatingDocumentResult, RatingModel>(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      ref: 'Client',
      required: true
    },
    carId: {
      type: Schema.Types.ObjectId,
      ref: 'Car',
      required: true
    },
    comment: {
      type: String
    },
    rating: {
      type: Number,
      min: [1, 'Rating must be at least 1'],
      max: [10, 'Rating must be at most 10'],
      required: true
    }
  },
  {
    timestamps: true
  }
);

const Rating = mongoose.model<RatingDocumentResult, RatingModel>('Rating', ratingSchema);
export default Rating;
