<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Email Template</title>
    <style>
      /* General reset styles for HTML emails */ body, table, td, a { -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%; font-family: "Open Sans", sans-serif; } table, td { mso-table-rspace: 0pt;
      mso-table-lspace: 0pt; } img { -ms-interpolation-mode: bicubic; } img, table { border: 0; } table {
      border-collapse: collapse !important; } body { width: 100% !important; height: 100% !important; margin: 0;
      padding: 0; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; } a { text-decoration: none;
      color: inherit; } /* Custom styles */ .container { max-width: 600px; width: 100%; background-color: #ffffff;
      margin: 20px auto; /* Add space outside the container */ padding: 20px; /* Add space inside the container */
      border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); } .header, .footer { padding:
      20px; text-align: center; background-color: #ffffff; } .footer { font-size: 12px; color: #666666; } .content {
      padding: 20px; text-align: center; } .btn { display: inline-block; padding: 10px 20px; background-color: #3b82f6;
      color: #ffffff; border-radius: 4px; font-weight: bold; margin-top: 20px; } .btn:hover { background-color: #2563eb;
      } .highlight { color: #2563eb; }
    </style>
  </head>
  <body>
    <table border='0' cellpadding='0' cellspacing='0' width='100%'>
      <tr>
        <td align='center' bgcolor='#f1f5f9'>
          <table class='container' cellpadding='0' cellspacing='0' style='padding: 20px; margin: 20px 0;'>
            <tr>
              <td class='header'>
                <span class='highlight' style='font-size: 24px; font-weight: bold;'>LUXLET</span>
                <br />
              </td>
            </tr>
            <tr>
              <td class='content'>
                <h1 style='font-size: 25px; font-weight: bold; margin: 20px 0;'>Transaction Successful</h1>
                <p style='font-size: 16px; line-height: 24px; color: #444444; margin: 20px 0;'>
                  Hello
                  {{firstName}}
                  {{lastName}},
                  <br />
                  Your transaction was successful. Here are the details:
                </p>

                <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                  <tr style='background-color: #f8fafc;'>
                    <td style='padding: 10px; border: 1px solid #e2e8f0; font-weight: bold;'>Transaction Reference</td>
                    <td style='padding: 10px; border: 1px solid #e2e8f0;'>{{reference}}</td>
                  </tr>
                  <tr>
                    <td style='padding: 10px; border: 1px solid #e2e8f0; font-weight: bold;'>Amount</td>
                    <td style='padding: 10px; border: 1px solid #e2e8f0;'>{{currency}} {{amount}}</td>
                  </tr>
                  <tr style='background-color: #f8fafc;'>
                    <td style='padding: 10px; border: 1px solid #e2e8f0; font-weight: bold;'>Car</td>
                    <td style='padding: 10px; border: 1px solid #e2e8f0;'>{{car.brand.name}} {{car.model}}</td>
                  </tr>
                  <tr>
                    <td style='padding: 10px; border: 1px solid #e2e8f0; font-weight: bold;'>Booking Period</td>
                    <td style='padding: 10px; border: 1px solid #e2e8f0;'>{{startDate}} to {{endDate}}</td>
                  </tr>
                  <tr style='background-color: #f8fafc;'>
                    <td style='padding: 10px; border: 1px solid #e2e8f0; font-weight: bold;'>Number of Days</td>
                    <td style='padding: 10px; border: 1px solid #e2e8f0;'>{{numberOfDays}}</td>
                  </tr>
                </table>

                <p style='font-size: 16px; line-height: 24px; color: #444444; margin: 20px 0;'>
                  Thank you for choosing LuxLet for your car rental needs.
                </p>
              </td>
            </tr>
            <tr>
              <td class='footer'>
                <p>© {{year}} LuxLet. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>