import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';
import { MongoClient } from 'mongodb';
import mongoose from 'mongoose';
import request from 'supertest';

import { fleetSeedData, fleetUserDataWithValidAddress } from '../../../__mocks__/fleet_user_mock.js';
import app from '../../app.js';
import Company from '../../models/CompanyModel/CompanyModel.js';
import User from '../../models/UserModel/UserModel.js';

describe('POST /auth/fleet/create', () => {
  let connection;
  let db;

  beforeEach(async () => {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      await collections[key].deleteMany({});
    }
  });

  beforeAll(async () => {
    process.env.JWT_EXPIRE = '24hr';
    connection = await MongoClient.connect(global.__MONGO_URI__, {});
    db = await connection.db(global.__MONGO_DB_NAME__);
    await mongoose.connect(global.__MONGO_URI__);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await connection.close();
    if (db.close) {
      await db.close();
    }
  });

  it('should return validation error for invalid input', async () => {
    const invalidRequestBody = {
      role: 'manager',
      email: 'invalid-email',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      password: 'password123'
    };

    const res = await request(app).post('/auth/fleet/create').send(invalidRequestBody);
    expect(res.statusCode).toBe(422);
    expect(res.body).toHaveProperty('error');
  });

  it('should return error if fleet user already exists', async () => {
    // Create a user to simulate duplicate
    const newUser = new User(fleetUserDataWithValidAddress);
    await newUser.save();
    const res = await request(app).post('/auth/fleet/create').send(fleetSeedData);
    expect(res.body).toHaveProperty('error', 'Fleet User Already Exist');
  });

  it('should create a new fleet user and company', async () => {
    const res = await request(app).post('/auth/fleet/create').send(fleetSeedData);

    expect(res.statusCode).toBe(201);
    expect(res.body.data).toHaveProperty('authToken');
    expect(res.body.data).toHaveProperty('email', fleetSeedData.email);

    const userInDb = await User.findOne({ email: fleetSeedData.email });
    expect(userInDb).toBeTruthy(); // Ensure the user was saved to the DB

    const companyInDb = await Company.findOne({ user: userInDb?._id });
    expect(companyInDb).toBeTruthy(); // Ensure the company was created
  });
});
