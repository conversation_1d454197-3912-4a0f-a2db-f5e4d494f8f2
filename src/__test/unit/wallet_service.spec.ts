import { afterAll, beforeAll, beforeEach, describe, expect, it } from '@jest/globals';
import mongoose from 'mongoose';

import Company from '../../models/CompanyModel/CompanyModel.js';
import Transaction from '../../models/TransactionModel/TransactionModel.js';
import User from '../../models/UserModel/UserModel.js';
import Wallet from '../../models/WalletModel/WalletModel.js';
import { WalletService } from '../../services/wallet_service.js';
import { TRANSACTION_TYPE } from '../../types/transactions.js';

describe('WalletService', () => {
  let testCompanyId: mongoose.Types.ObjectId;
  let testUserId: mongoose.Types.ObjectId;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/luxlet-test');
  });

  beforeEach(async () => {
    // Clean up test data
    await Wallet.deleteMany({});
    await Transaction.deleteMany({});
    await Company.deleteMany({});
    await User.deleteMany({});

    // Create test company and user
    testCompanyId = new mongoose.Types.ObjectId();
    testUserId = new mongoose.Types.ObjectId();

    const testUser = new User({
      _id: testUserId,
      email: '<EMAIL>',
      phoneNumber: '+2348123456789',
      role: 'manager',
      company: testCompanyId,
      isActive: true
    });
    await testUser.save();

    const testCompany = new Company({
      _id: testCompanyId,
      companyName: 'Test Company',
      supportEmail: '<EMAIL>',
      supportPhone: '+2348123456789',
      address: {
        street: 'Test Street',
        city: 'Test City',
        state: 'Test State',
        country: 'Nigeria',
        longitude: 3.3792,
        latitude: 6.5244
      },
      user: testUserId,
      isActive: true
    });
    await testCompany.save();
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('createWallet', () => {
    it('should create a new wallet successfully', async () => {
      const wallet = await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });

      expect(wallet).toBeDefined();
      expect(wallet.company.toString()).toBe(testCompanyId.toString());
      expect(wallet.availableBalance).toBe(0);
      expect(wallet.grossEarnings).toBe(0);
      expect(wallet.netEarnings).toBe(0);
      expect(wallet.totalWithdrawn).toBe(0);
      expect(wallet.totalPlatformFees).toBe(0);
      expect(wallet.totalRefunds).toBe(0);
      expect(wallet.pendingEarnings).toBe(0);
    });

    it('should throw error when wallet already exists', async () => {
      // Create first wallet
      await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });

      // Try to create another wallet for the same company
      await expect(
        WalletService.createWallet({
          company: testCompanyId,
          createdBy: testUserId
        })
      ).rejects.toThrow('Wallet already exists for this company');
    });
  });

  describe('getWalletByCompany', () => {
    it('should return wallet for existing company', async () => {
      const createdWallet = await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });

      const wallet = await WalletService.getWalletByCompany(testCompanyId);
      expect(wallet).toBeDefined();
      expect(wallet._id.toString()).toBe(createdWallet._id.toString());
    });

    it('should return null for non-existing company', async () => {
      const nonExistentCompanyId = new mongoose.Types.ObjectId();
      const wallet = await WalletService.getWalletByCompany(nonExistentCompanyId);
      expect(wallet).toBeNull();
    });
  });

  describe('addEarnings', () => {
    beforeEach(async () => {
      await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });
    });

    it('should add earnings to wallet correctly', async () => {
      const grossAmount = 1000;
      const platformFee = 200;
      const netAmount = 800;

      const result = await WalletService.addEarnings(
        testCompanyId,
        grossAmount,
        netAmount,
        platformFee,
        {
          amount: netAmount,
          description: 'Test earnings',
          reference: 'TEST_001',
          metadata: { test: true }
        },
        testUserId
      );

      expect(result.wallet.grossEarnings).toBe(grossAmount);
      expect(result.wallet.netEarnings).toBe(netAmount);
      expect(result.wallet.totalPlatformFees).toBe(platformFee);
      expect(result.wallet.availableBalance).toBe(netAmount);
      expect(result.transaction.amount).toBe(netAmount);
      expect(result.transaction.type).toBe(TRANSACTION_TYPE.BOOKING_EARNINGS);
    });

    it('should create platform fee transaction when fee > 0', async () => {
      const grossAmount = 1000;
      const platformFee = 200;
      const netAmount = 800;

      await WalletService.addEarnings(
        testCompanyId,
        grossAmount,
        netAmount,
        platformFee,
        {
          amount: netAmount,
          description: 'Test earnings',
          reference: 'TEST_001'
        },
        testUserId
      );

      const feeTransaction = await Transaction.findOne({
        companyId: testCompanyId,
        type: TRANSACTION_TYPE.PLATFORM_FEE
      });

      expect(feeTransaction).toBeDefined();
      expect(feeTransaction.amount).toBe(platformFee);
    });
  });

  describe('processWithdrawal', () => {
    beforeEach(async () => {
      await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });

      // Add some earnings first
      await WalletService.addEarnings(
        testCompanyId,
        1000,
        800,
        200,
        {
          amount: 800,
          description: 'Initial earnings',
          reference: 'INIT_001'
        },
        testUserId
      );
    });

    it('should process withdrawal successfully', async () => {
      const withdrawalAmount = 500;

      const result = await WalletService.processWithdrawal(
        testCompanyId,
        withdrawalAmount,
        {
          amount: withdrawalAmount,
          description: 'Test withdrawal',
          reference: 'WITHDRAW_001'
        },
        testUserId
      );

      expect(result.wallet.totalWithdrawn).toBe(withdrawalAmount);
      expect(result.wallet.availableBalance).toBe(800 - withdrawalAmount);
      expect(result.transaction.type).toBe(TRANSACTION_TYPE.WALLET_WITHDRAWAL);
    });

    it('should throw error for insufficient balance', async () => {
      const withdrawalAmount = 1000; // More than available balance (800)

      await expect(
        WalletService.processWithdrawal(
          testCompanyId,
          withdrawalAmount,
          {
            amount: withdrawalAmount,
            description: 'Test withdrawal',
            reference: 'WITHDRAW_001'
          },
          testUserId
        )
      ).rejects.toThrow('Insufficient balance for withdrawal');
    });
  });

  describe('calculatePlatformFee', () => {
    it('should calculate 20% platform fee by default', () => {
      const grossAmount = 1000;
      const fee = WalletService.calculatePlatformFee(grossAmount);
      expect(fee).toBe(200);
    });

    it('should calculate custom platform fee percentage', () => {
      const grossAmount = 1000;
      const customPercentage = 0.15; // 15%
      const fee = WalletService.calculatePlatformFee(grossAmount, customPercentage);
      expect(fee).toBe(150);
    });
  });

  describe('reconcileWallet', () => {
    beforeEach(async () => {
      await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });
    });

    it('should reconcile wallet balances with transaction history', async () => {
      // Add some earnings
      await WalletService.addEarnings(
        testCompanyId,
        1000,
        800,
        200,
        {
          amount: 800,
          description: 'Earnings 1',
          reference: 'EARN_001'
        },
        testUserId
      );

      // Process withdrawal
      await WalletService.processWithdrawal(
        testCompanyId,
        300,
        {
          amount: 300,
          description: 'Withdrawal 1',
          reference: 'WITHDRAW_001'
        },
        testUserId
      );

      // Manually corrupt wallet data
      await Wallet.findOneAndUpdate(
        { company: testCompanyId },
        { grossEarnings: 500, netEarnings: 400, availableBalance: 100 }
      );

      // Reconcile
      const reconciledWallet = await WalletService.reconcileWallet(testCompanyId);

      expect(reconciledWallet.grossEarnings).toBe(800); // Only net earnings from transactions
      expect(reconciledWallet.totalPlatformFees).toBe(200);
      expect(reconciledWallet.netEarnings).toBe(600); // 800 - 200
      expect(reconciledWallet.totalWithdrawn).toBe(300);
      expect(reconciledWallet.availableBalance).toBe(300); // 600 - 300
      expect(reconciledWallet.lastReconciledAt).toBeDefined();
    });
  });

  describe('validateWalletConsistency', () => {
    beforeEach(async () => {
      await WalletService.createWallet({
        company: testCompanyId,
        createdBy: testUserId
      });
    });

    it('should validate consistent wallet', async () => {
      const validation = await WalletService.validateWalletConsistency(testCompanyId);
      expect(validation.isConsistent).toBe(true);
      expect(validation.discrepancies).toHaveLength(0);
    });

    it('should detect inconsistent wallet', async () => {
      // Manually corrupt wallet data
      await Wallet.findOneAndUpdate(
        { company: testCompanyId },
        {
          grossEarnings: 1000,
          netEarnings: 900, // Should be 800 (1000 - 200)
          totalPlatformFees: 200,
          availableBalance: 800 // Should be 800 (900 - 100)
        }
      );

      const validation = await WalletService.validateWalletConsistency(testCompanyId);
      expect(validation.isConsistent).toBe(false);
      expect(validation.discrepancies.length).toBeGreaterThan(0);
    });
  });
});
