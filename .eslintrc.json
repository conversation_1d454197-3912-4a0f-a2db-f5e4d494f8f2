{"root": true, "env": {"browser": true, "es2021": true, "jest": true}, "overrides": [], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier", "simple-import-sort"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_|^req|^res|^next"}], "@typescript-eslint/consistent-type-definitions": ["error", "type"], "indent": ["warn", 2, {"SwitchCase": 1}], "quotes": ["error", "single", {"allowTemplateLiterals": true}], "no-console": ["error", {"allow": ["warn", "error"]}], "no-var": "error", "prefer-const": "warn", "no-duplicate-imports": ["error", {"includeExports": true}], "no-multi-spaces": "error", "no-lonely-if": "error", "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": false}], "simple-import-sort/imports": "error", "simple-import-sort/exports": "error"}}