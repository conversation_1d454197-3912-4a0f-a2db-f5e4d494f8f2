{"name": "luxlet-backend-services", "version": "1.0.0", "description": "", "main": "index.ts", "type": "module", "engines": {"node": ">=20.19.0"}, "scripts": {"build": "tsc -p tsconfig.json && copyfiles -u 1 \"src/email_templates/**/*\" dist/email_templates && copyfiles -u 1 \"src/email_templates/**/*\" dist/src/email_templates", "start": "ts-node-esm ./src/index.ts | pino-pretty", "dev": "nodemon --exec node --env-file=.env.development --loader ts-node/esm src/index.ts", "local": "nodemon --exec node --env-file=.env.local --loader ts-node/esm src/index.ts", "production": "nodemon --exec node --env-file=.env.production --loader ts-node/esm src/index.ts", "test": "NODE_OPTIONS=--experimental-vm-modules npx jest", "test:watch": "jest --watch", "lint": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "lint-fix": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts . --fix", "format": "npx prettier --write .", "prepare": "husky && husky install"}, "keywords": [], "author": "kadismile", "license": "ISC", "devDependencies": {"@eslint/js": "^9.8.0", "@jest/globals": "^29.7.0", "@shelf/jest-mongodb": "^4.1.7", "@types/agenda": "^4.1.0", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/google-libphonenumber": "^7.4.30", "@types/hapi__joi": "^17.1.14", "@types/jest": "^29.5.13", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.15", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "body-parser": "^1.20.2", "copyfiles": "^2.4.1", "eslint": "^8.39.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^12.0.0", "express": "^4.19.2", "globals": "^15.8.0", "jest": "^29.7.0", "lint-staged": "^15.2.7", "mongodb-memory-server": "^10.0.1", "nodemon": "^3.1.4", "pino-pretty": "^11.2.2", "prettier": "^3.3.3", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.4", "typescript-eslint": "^7.17.0"}, "dependencies": {"@hapi/joi": "^17.1.1", "africastalking": "^0.7.3", "agenda": "^5.0.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cloudinary": "^2.4.0", "cors": "^2.8.5", "firebase-admin": "^13.0.2", "google-libphonenumber": "^3.2.40", "handlebars": "^4.7.8", "husky": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongodb": "^6.8.0", "mongoose": "^8.9.5", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.8", "node-cron": "^3.0.3", "nodemailer": "^6.9.14", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.4.3", "winston": "^3.13.1"}, "lint-staged": {"src/**/*.{js,ts,tsx}": ["npm run lint-fix", "npm run format"]}}