export const userSeedData = [
  {
    email: '<EMAIL>',
    password: '111222333',
    role: 'admin',
    phoneNumber: '070687983458',
    firstName: 'Okhai',
    middleName: 'Okahi',
    lastName: 'Okahi',
    fcmToken: 'LS1CRUdJTiBSU0WEIOJklsdlopjsd',
    deviceId: 'erprpfklkmnsdiojweoilksjhfhf',
    address: {
      fullAddress: 'area 2 garki Abuja',
      longitude: '9.0323',
      latitude: '7.4692',
      state: 'Abuja FCT',
      city: 'Abuja',
      lga: 'AMAC',
      countryCode: 'NG',
      country: 'Nigeria',
      neighborhood: 'Area 2'
    }
  }
];

export const fleetSeedData = {
  email: '<EMAIL>',
  password: '111222333',
  role: 'manager',
  phoneNumber: '070688983123',
  firstName: 'Bonku',
  middleName: 'Ogie',
  lastName: 'Okahi',
  fcmToken: 'LS1CRUdJTiBSU0WEI',
  deviceId: 'erprpfklkmnsdioj',
  address: {
    fullAddress: 'Area 2 garki Abuja',
    longitude: '9.0323',
    latitude: '7.4692',
    state: 'Abuja FCT',
    city: 'Abuja',
    lga: 'AMAC',
    countryCode: 'NG',
    country: 'Nigeria',
    neighborhood: 'Area 2'
  }
};

export const brandSeedData = [
  {
    _id: '66e950ed621de319297b242e',
    name: 'Toyota'
  },
  {
    _id: '66e950fb621de319297b2431',
    name: 'Rolls Royce'
  },
  {
    _id: '66e95104621de319297b2434',
    name: 'Lexus'
  },
  {
    _id: '66e9510c621de319297b2437',
    name: 'Mercedes-Benz'
  },
  {
    _id: '66e95115621de319297b243a',
    name: 'Ford'
  },
  {
    _id: '66e9511b621de319297b243d',
    name: 'Chevrolet'
  },
  {
    _id: '66e95120621de319297b2440',
    name: 'Dodge'
  },
  {
    _id: '66e95129621de319297b2443',
    name: 'BMW'
  },
  {
    _id: '66e9512e621de319297b2446',
    name: 'Audi'
  },
  {
    _id: '66e95136621de319297b2449',
    name: 'Lincoln'
  },
  {
    _id: '66e9513b621de319297b244c',
    name: 'Cadillac'
  },
  {
    _id: '66e95141621de319297b244f',
    name: 'Chrysler'
  },
  {
    _id: '66e95147621de319297b2452',
    name: 'IVM'
  }
];

export const carCategoriesSeedData = [
  {
    _id: '66e95071621de319297b2422',
    name: 'Sports',
    pictureUrl: 'https://cdn.motor1.com/images/mgl/P33WYL/s1/ferrari-sp48-unica.jpg'
  },
  {
    _id: '66e9508f621de319297b2425',
    name: 'Wedding',
    pictureUrl:
      'https://c8.alamy.com/comp/2APRPNE/brussels-jan-9-2020-new-bentley-flying-spur-luxury-car-model-showcased-at-the-brussels-autosalon-2020-motor-show-2APRPNE.jpg'
  },
  {
    _id: '66e9509b621de319297b2428',
    name: 'SUV',
    pictureUrl:
      'https://media.istockphoto.com/id/1148058147/photo/red-generic-suv-on-black-background.jpg?s=612x612&w=0&k=20&c=sEmgHihgWytks495jWLHz1hy6xfz5S_9PXntiUn8UDE='
  },
  {
    _id: { $oid: '66e950aa621de319297b242b' },
    name: 'Sedan',
    isActive: true,
    pictureUrl:
      'https://media.istockphoto.com/id/1185460602/photo/3d-illustration-of-generic-red-sedan-car-front-side-view.jpg?s=612x612&w=0&k=20&c=bi8lRPp8xK_oFTDhkWQLk0I0LdiCgzcffXJnwAbSmR0='
  }
];

export const fleetUserDataWithValidAddress = {
  email: '<EMAIL>',
  password: '111222333',
  role: 'manager',
  phoneNumber: '070688983123',
  firstName: 'Bonku',
  middleName: 'Ogie',
  lastName: 'Okahi',
  fcmToken: 'LS1CRUdJTiBSU0WEI',
  deviceId: 'erprpfklkmnsdioj',
  address: {
    fullAddress: 'Area 2 garki Abuja',
    location: {
      type: 'Point',
      coordinates: [1.29899, 3.48949]
    },
    longitude: '9.0323',
    latitude: '7.4692',
    state: 'Abuja FCT',
    city: 'Abuja',
    lga: 'AMAC',
    countryCode: 'NG',
    country: 'Nigeria',
    neighborhood: 'Area 2'
  }
};
