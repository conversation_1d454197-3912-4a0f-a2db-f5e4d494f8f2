# LuxLet Backend Service

LuxLet is a luxury car rental platform that connects car owners with clients looking to rent high-end vehicles. This repository contains the backend service that powers the LuxLet application.

## 🚀 Features

- **User Management**: Registration, authentication, and profile management for different user roles (clients, managers, admins)
- **Car Management**: Add, update, and manage car listings with details, images, and availability
- **Booking System**: Complete booking flow with scheduling, payments, and notifications
- **Payment Processing**: Integration with Paystack for secure payment processing
- **Notifications**: Push notifications via Firebase Cloud Messaging and WhatsApp messaging via Twilio
- **Reviews & Ratings**: Allow clients to rate and review their rental experiences
- **Wallet System**: Manage earnings and transactions for car owners and managers

## 🛠️ Tech Stack

- **Node.js** with **Express.js** framework
- **TypeScript** for type safety
- **MongoDB** with **Mongoose** for data storage
- **JWT** for authentication
- **Firebase Admin SDK** for push notifications
- **Twilio** for WhatsApp messaging
- **Paystack** for payment processing
- **Cloudinary** for image storage
- **Nodemailer** for email notifications
- **Swagger** for API documentation

## 📋 Prerequisites

- Node.js (v20.19.0 or higher)
- MongoDB
- Firebase project with FCM enabled
- Paystack account
- Twilio account (for WhatsApp messaging)
- Cloudinary account (for image storage)

## 🔧 Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/luxlet-backend-service.git
   cd luxlet-backend-service
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:
   Create `.env.development`, `.env.local`, and `.env.production` files based on your environment needs. Required variables include:

   ```
   PORT=8000
   MONGO_URL=mongodb://localhost:27017/luxlet
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRE=30d

   # Firebase
   FIREBASE_TYPE=
   FIREBASE_PROJECT_ID=
   FIREBASE_PRIVATE_KEY_ID=
   FIREBASE_PRIVATE_KEY=
   FIREBASE_CLIENT_EMAIL=
   FIREBASE_CLIENT_ID=
   FIREBASE_AUTH_URI=
   FIREBASE_TOKEN_URI=
   FIREBASE_AUTH_PROVIDER_X509_CERT_URL=
   FIREBASE_CLIENT_X509_CERT_URL=

   # Paystack
   PAYSTACK_SECRET_KEY=
   PAYSTACK_BASE_URL=https://api.paystack.co

   # Twilio (for WhatsApp)
   TWILIO_ACCOUNT_SID=
   TWILIO_AUTH_TOKEN=
   TWILIO_FROM=

   # Email
   SMTP_HOST=
   SMTP_PORT=
   SMTP_USER=
   SMTP_PASSWORD=
   FROM_EMAIL=
   FROM_NAME=
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## 🏃‍♂️ Running the Application

- **Development mode**: `npm run dev`
- **Local mode**: `npm run local`
- **Production mode**: `npm run production`
- **Build for production**: `npm run build`
- **Start production build**: `npm start`

## 🧪 Testing

Run tests using Jest:

```bash
npm test
```

Run tests in watch mode:

```bash
npm run test:watch
```

## 📚 API Documentation

API documentation is available via Swagger UI at `/api/docs` when the server is running.

## 📁 Project Structure

```
luxlet-backend-service/
├── src/
│   ├── api_validators/     # Request validation schemas
│   ├── controllers/        # Route controllers
│   ├── email_templates/    # Email templates using Handlebars
│   ├── helpers/            # Utility functions
│   ├── libs/               # Core libraries
│   ├── middleware/         # Express middleware
│   ├── models/             # Mongoose models
│   ├── routes/             # API routes
│   ├── types/              # TypeScript type definitions
│   ├── app.ts              # Express app setup
│   └── index.ts            # Application entry point
├── __mocks__/              # Test mocks
├── config/                 # Configuration files
└── dist/                   # Compiled JavaScript output
```

## 🔄 Key Workflows

### Booking Flow

1. Client searches for available cars
2. Client creates a booking (status: PENDING)
3. Client makes payment via Paystack
4. On successful payment, booking status changes to BOOKED
5. Conflicting bookings are automatically canceled
6. Notifications are sent to both client and car manager
7. After the rental period, client can leave a rating and review

### Notification System

- Push notifications via Firebase Cloud Messaging (FCM)
- WhatsApp messages via Twilio
- Email notifications via Nodemailer with Handlebars templates

## 🔒 Security

- Password hashing with bcrypt
- JWT-based authentication
- Role-based access control
- Input validation with Joi

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License.
